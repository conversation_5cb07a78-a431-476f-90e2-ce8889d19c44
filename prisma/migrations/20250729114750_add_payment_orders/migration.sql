-- CreateTable
CREATE TABLE "payment_orders" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "outTradeNo" TEXT NOT NULL,
    "orderNo" TEXT,
    "payNo" TEXT,
    "amount" REAL NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "payChannel" TEXT,
    "tradeType" TEXT,
    "codeUrl" TEXT,
    "qrcodeUrl" TEXT,
    "attach" TEXT,
    "successTime" DATETIME,
    "expireTime" DATETIME,
    "notifyData" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "payment_orders_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "payment_orders_outTradeNo_key" ON "payment_orders"("outTradeNo");
