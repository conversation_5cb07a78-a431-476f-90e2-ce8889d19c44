/*
  Warnings:

  - You are about to drop the column `minPointsRequired` on the `users` table. All the data in the column will be lost.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_users" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "username" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'USER',
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "totalTokensUsed" INTEGER NOT NULL DEFAULT 0,
    "totalCost" REAL NOT NULL DEFAULT 0.0,
    "monthlyTokensUsed" INTEGER NOT NULL DEFAULT 0,
    "monthlyCost" REAL NOT NULL DEFAULT 0.0,
    "lastResetAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "pointsBalance" REAL NOT NULL DEFAULT 0.0
);
INSERT INTO "new_users" ("createdAt", "email", "enabled", "id", "lastResetAt", "monthlyCost", "monthlyTokensUsed", "password", "pointsBalance", "role", "totalCost", "totalTokensUsed", "updatedAt", "username") SELECT "createdAt", "email", "enabled", "id", "lastResetAt", "monthlyCost", "monthlyTokensUsed", "password", "pointsBalance", "role", "totalCost", "totalTokensUsed", "updatedAt", "username" FROM "users";
DROP TABLE "users";
ALTER TABLE "new_users" RENAME TO "users";
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
