-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_model_pricing" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "model" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "billingType" TEXT NOT NULL DEFAULT 'TOKEN',
    "inputTokenPrice" REAL NOT NULL,
    "outputTokenPrice" REAL NOT NULL,
    "countPrice" REAL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_model_pricing" ("createdAt", "description", "enabled", "id", "inputTokenPrice", "model", "outputTokenPrice", "provider", "updatedAt") SELECT "createdAt", "description", "enabled", "id", "inputTokenPrice", "model", "outputTokenPrice", "provider", "updatedAt" FROM "model_pricing";
DROP TABLE "model_pricing";
ALTER TABLE "new_model_pricing" RENAME TO "model_pricing";
CREATE UNIQUE INDEX "model_pricing_model_key" ON "model_pricing"("model");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
