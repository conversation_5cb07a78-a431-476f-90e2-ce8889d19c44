import { PrismaClient } from '../app/generated/prisma';

const prisma = new PrismaClient();

// 模型定价数据（基于实际API定价，简化为统一定价）
const modelPricingData = [
  // OpenAI 模型
  { model: 'gpt-4', provider: 'openai', inputTokenPrice: 0.03, outputTokenPrice: 0.06 },
  { model: 'gpt-4-turbo', provider: 'openai', inputTokenPrice: 0.01, outputTokenPrice: 0.03 },
  { model: 'gpt-4o', provider: 'openai', inputTokenPrice: 0.005, outputTokenPrice: 0.015 },
  { model: 'gpt-4o-mini', provider: 'openai', inputTokenPrice: 0.00015, outputTokenPrice: 0.0006 },
  { model: 'gpt-3.5-turbo', provider: 'openai', inputTokenPrice: 0.0015, outputTokenPrice: 0.002 },
  
  // Anthropic 模型
  { model: 'claude-3-opus-20240229', provider: 'anthropic', inputTokenPrice: 0.015, outputTokenPrice: 0.075 },
  { model: 'claude-3-sonnet-20240229', provider: 'anthropic', inputTokenPrice: 0.003, outputTokenPrice: 0.015 },
  { model: 'claude-3-haiku-20240307', provider: 'anthropic', inputTokenPrice: 0.00025, outputTokenPrice: 0.00125 },
  { model: 'claude-3-5-sonnet-20241022', provider: 'anthropic', inputTokenPrice: 0.003, outputTokenPrice: 0.015 },
  
  // Google 模型
  { model: 'gemini-pro', provider: 'google', inputTokenPrice: 0.0005, outputTokenPrice: 0.0015 },
  { model: 'gemini-pro-vision', provider: 'google', inputTokenPrice: 0.0005, outputTokenPrice: 0.0015 },
  { model: 'gemini-1.5-pro', provider: 'google', inputTokenPrice: 0.0035, outputTokenPrice: 0.0105 },
  { model: 'gemini-1.5-flash', provider: 'google', inputTokenPrice: 0.000075, outputTokenPrice: 0.0003 },
  
  // 其他服务商（统一定价）
  { model: 'doubao-pro-4k', provider: 'bytedance', inputTokenPrice: 0.0008, outputTokenPrice: 0.002 },
  { model: 'doubao-pro-32k', provider: 'bytedance', inputTokenPrice: 0.0008, outputTokenPrice: 0.002 },
  { model: 'qwen-turbo', provider: 'alibaba', inputTokenPrice: 0.0003, outputTokenPrice: 0.0006 },
  { model: 'qwen-plus', provider: 'alibaba', inputTokenPrice: 0.0004, outputTokenPrice: 0.0012 },
  { model: 'moonshot-v1-8k', provider: 'moonshot', inputTokenPrice: 0.0012, outputTokenPrice: 0.0012 },
  { model: 'moonshot-v1-32k', provider: 'moonshot', inputTokenPrice: 0.0024, outputTokenPrice: 0.0024 },
  { model: 'deepseek-chat', provider: 'deepseek', inputTokenPrice: 0.00014, outputTokenPrice: 0.00028 },
  { model: 'grok-beta', provider: 'xai', inputTokenPrice: 0.005, outputTokenPrice: 0.015 },
];

async function seedModelPricing() {
  console.log('开始初始化模型定价数据...');
  
  for (const pricing of modelPricingData) {
    await prisma.modelPricing.upsert({
      where: { model: pricing.model },
      update: {
        provider: pricing.provider,
        inputTokenPrice: pricing.inputTokenPrice,
        outputTokenPrice: pricing.outputTokenPrice,
      },
      create: {
        model: pricing.model,
        provider: pricing.provider,
        inputTokenPrice: pricing.inputTokenPrice,
        outputTokenPrice: pricing.outputTokenPrice,
        description: `${pricing.provider} ${pricing.model} 模型定价`,
      },
    });
  }
  
  console.log(`已初始化 ${modelPricingData.length} 个模型的定价数据`);
}

async function main() {
  try {
    await seedModelPricing();
    console.log('模型定价数据初始化完成！');
  } catch (error) {
    console.error('初始化失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { seedModelPricing };
