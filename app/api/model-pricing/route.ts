import { NextRequest, NextResponse } from "next/server";
import { verifyUserTokenWithStatus } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

// 获取模型定价信息（普通用户可访问）
export async function GET(req: NextRequest) {
  try {
    // 验证用户权限（普通用户即可）
    const authResult = await verifyUserTokenWithStatus(req);
    if (authResult.error) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // 获取所有启用的模型定价
    const modelPricings = await prisma.modelPricing.findMany({
      where: {
        enabled: true,
      },
      select: {
        id: true,
        model: true,
        billingType: true,
        inputTokenPrice: true,
        outputTokenPrice: true,
        countPrice: true,
        enabled: true,
        description: true,
      },
      orderBy: [{ model: "asc" }],
    });

    return NextResponse.json({
      success: true,
      data: modelPricings,
    });
  } catch (error) {
    console.error("Get model pricing error:", error);
    return NextResponse.json({ error: "获取模型定价失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
