import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import { verifyUserTokenWithStatus } from "@/app/lib/jwt";

export async function GET(req: NextRequest) {
  try {
    // 验证用户身份
    const authResult = await verifyUserTokenWithStatus(req);

    if (authResult.error) {
      const statusCode = authResult.error === "未授权访问" ? 401 : 403;
      return NextResponse.json(
        { error: authResult.error },
        { status: statusCode },
      );
    }

    const userId = authResult.payload!.userId;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {
      userId,
    };

    if (status && status !== "ALL") {
      where.status = status;
    }

    // 获取订单列表
    const [orders, total] = await Promise.all([
      prisma.paymentOrder.findMany({
        where,
        select: {
          id: true,
          outTradeNo: true,
          amount: true,
          status: true,
          payChannel: true,
          tradeType: true,
          createdAt: true,
          successTime: true,
          expireTime: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.paymentOrder.count({ where }),
    ]);

    // 获取用户充值统计
    const stats = await prisma.paymentOrder.groupBy({
      by: ["status"],
      where: {
        userId,
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
    });

    const statsMap = stats.reduce(
      (acc, stat) => {
        acc[stat.status] = {
          count: stat._count.id,
          amount: stat._sum.amount || 0,
        };
        return acc;
      },
      {} as Record<string, { count: number; amount: number }>,
    );

    return NextResponse.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats: statsMap,
      },
    });
  } catch (error) {
    console.error("Get user orders error:", error);
    return NextResponse.json({ error: "获取订单记录失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
