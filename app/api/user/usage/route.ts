import { NextRequest, NextResponse } from "next/server";
import { verifyUserToken } from "@/app/lib/jwt";
import {
  getUserUsageStats,
  calculateUserBill,
  checkUserUsageLimit,
} from "@/app/lib/billing-service";

// 获取用户使用量统计
export async function GET(req: NextRequest) {
  try {
    // 验证用户token
    const tokenData = verifyUserToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要登录" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const planId = searchParams.get("planId") || "free";

    // 解析日期参数
    let start: Date | undefined;
    let end: Date | undefined;

    if (startDate) {
      start = new Date(startDate);
    }
    if (endDate) {
      end = new Date(endDate);
    }

    // 获取使用统计
    const [usageStats, bill, usageLimit] = await Promise.all([
      getUserUsageStats(tokenData.userId, start, end),
      calculateUserBill(tokenData.userId, planId),
      checkUserUsageLimit(tokenData.userId, planId),
    ]);

    console.log(`[User Usage] Stats requested by ${tokenData.username}`);

    return NextResponse.json({
      success: true,
      data: {
        usage: usageStats,
        billing: bill,
        limits: usageLimit,
      },
    });
  } catch (error) {
    console.error("Get user usage error:", error);
    return NextResponse.json({ error: "获取使用统计失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
