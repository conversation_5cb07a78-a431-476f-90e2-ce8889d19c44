import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import { verifyUserToken } from "@/app/lib/jwt";

export async function GET(req: NextRequest) {
  try {
    // 验证用户身份
    const tokenData = verifyUserToken(req);
    if (!tokenData) {
      return NextResponse.json(
        { success: false, error: "未授权访问" },
        { status: 401 },
      );
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // 构建查询条件
    const where: any = {
      userId: tokenData.userId,
    };

    // 添加日期筛选
    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate + "T00:00:00.000Z"),
        lte: new Date(endDate + "T23:59:59.999Z"),
      };
    }

    // 查询API调用日志
    const logs = await prisma.apiLog.findMany({
      where,
      select: {
        id: true,
        model: true,
        tokensUsed: true,
        promptTokens: true,
        completionTokens: true,
        cost: true,
        status: true,
        errorMsg: true,
        duration: true,
        createdAt: true,
        provider: {
          select: {
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return NextResponse.json({
      success: true,
      logs,
      pagination: {
        page,
        limit,
        total: await prisma.apiLog.count({ where }),
      },
    });
  } catch (error) {
    console.error("Get user history error:", error);
    return NextResponse.json(
      { success: false, error: "获取使用历史失败" },
      { status: 500 },
    );
  }
}
