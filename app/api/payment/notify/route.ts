import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import {
  getPaymentConfig,
  verifySign,
  type PaymentNotify,
} from "@/app/lib/payment-service";

export async function POST(req: NextRequest) {
  try {
    // 获取支付配置
    const paymentConfig = getPaymentConfig();

    // 解析表单数据
    const formData = await req.formData();
    const notifyData: Record<string, string> = {};

    formData.forEach((value, key) => {
      notifyData[key] = value.toString();
    });

    console.log("Payment notify received:", notifyData);

    // 验证必要参数
    const requiredFields = [
      "code",
      "timestamp",
      "mch_id",
      "order_no",
      "out_trade_no",
      "pay_no",
      "total_fee",
      "sign",
    ];
    for (const field of requiredFields) {
      if (!notifyData[field]) {
        console.error(`Missing required field: ${field}`);
        return new NextResponse("FAIL", { status: 400 });
      }
    }

    // 验证商户号
    if (notifyData.mch_id !== paymentConfig.mchId) {
      console.error("Invalid merchant ID");
      return new NextResponse("FAIL", { status: 400 });
    }

    // 验证签名
    const { sign, ...paramsForSign } = notifyData;
    if (!verifySign(paramsForSign, paymentConfig.apiKey, sign)) {
      console.error("Invalid signature");
      return new NextResponse("FAIL", { status: 400 });
    }

    // 查找订单
    const paymentOrder = await prisma.paymentOrder.findUnique({
      where: { outTradeNo: notifyData.out_trade_no },
      include: { user: true },
    });

    if (!paymentOrder) {
      console.error("Payment order not found:", notifyData.out_trade_no);
      return new NextResponse("FAIL", { status: 404 });
    }

    // 检查订单状态，避免重复处理
    if (paymentOrder.status === "PAID") {
      console.log("Order already processed:", notifyData.out_trade_no);
      return new NextResponse("SUCCESS", { status: 200 });
    }

    // 验证金额
    const notifyAmount = parseFloat(notifyData.total_fee);
    if (Math.abs(notifyAmount - paymentOrder.amount) > 0.01) {
      console.error("Amount mismatch:", {
        notify: notifyAmount,
        order: paymentOrder.amount,
      });
      return new NextResponse("FAIL", { status: 400 });
    }

    // 检查支付结果
    if (notifyData.code === "0") {
      // 支付成功，开始数据库事务
      await prisma.$transaction(async (tx) => {
        // 更新订单状态
        await tx.paymentOrder.update({
          where: { id: paymentOrder.id },
          data: {
            status: "PAID",
            orderNo: notifyData.order_no,
            payNo: notifyData.pay_no,
            payChannel: notifyData.pay_channel || "wxpay",
            tradeType: notifyData.trade_type || "NATIVE",
            successTime: notifyData.success_time
              ? new Date(notifyData.success_time)
              : new Date(),
            notifyData: JSON.stringify(notifyData),
          },
        });

        // 增加用户点数余额 (1:10比例)
        const pointsToAdd = paymentOrder.amount * 10;
        await tx.user.update({
          where: { id: paymentOrder.userId },
          data: {
            pointsBalance: {
              increment: pointsToAdd,
            },
          },
        });

        console.log(
          `[Payment] User ${paymentOrder.userId} balance increased by ${pointsToAdd} points (${paymentOrder.amount} yuan * 10)`,
        );
      });

      console.log("Payment processed successfully:", notifyData.out_trade_no);
      return new NextResponse("SUCCESS", { status: 200 });
    } else {
      // 支付失败
      await prisma.paymentOrder.update({
        where: { id: paymentOrder.id },
        data: {
          status: "FAILED",
          notifyData: JSON.stringify(notifyData),
        },
      });

      console.log("Payment failed:", notifyData.out_trade_no);
      return new NextResponse("SUCCESS", { status: 200 });
    }
  } catch (error) {
    console.error("Payment notify error:", error);
    return new NextResponse("FAIL", { status: 500 });
  }
}

export const runtime = "nodejs";
