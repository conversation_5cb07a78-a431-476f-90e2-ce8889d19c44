import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import {
  getPaymentConfig,
  callLTZFAPI,
  generateOrderNo,
  type PaymentRequest,
} from "@/app/lib/payment-service";
import jwt from "jsonwebtoken";

export async function POST(req: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "未授权访问" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    let userId: string;

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
        userId: string;
      };
      userId = decoded.userId;
    } catch (error) {
      return NextResponse.json(
        { success: false, message: "无效的访问令牌" },
        { status: 401 },
      );
    }

    // 验证用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 },
      );
    }

    // 解析请求参数
    const { amount } = await req.json();

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { success: false, message: "充值金额必须大于0" },
        { status: 400 },
      );
    }

    // 生成订单号
    const outTradeNo = generateOrderNo();

    // 获取支付配置
    const paymentConfig = getPaymentConfig();

    // 构建支付请求
    const paymentRequest: PaymentRequest = {
      outTradeNo,
      totalFee: amount.toFixed(2),
      body: `QADChat充值-${amount}元(获得${amount * 10}☁️Point)`,
      attach: `user_${userId}_amount_${amount}`, // 简化attach参数，避免JSON格式问题
      timeExpire: "30m", // 30分钟过期
    };

    // 调用蓝兔支付API
    const paymentResult = await callLTZFAPI(paymentConfig, paymentRequest);

    if (paymentResult.code !== 0) {
      return NextResponse.json(
        { success: false, message: paymentResult.msg || "支付接口调用失败" },
        { status: 500 },
      );
    }

    // 计算过期时间
    const expireTime = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期

    // 保存订单到数据库
    const paymentOrder = await prisma.paymentOrder.create({
      data: {
        userId,
        outTradeNo,
        amount,
        codeUrl: paymentResult.data!.code_url,
        qrcodeUrl: paymentResult.data!.QRcode_url,
        attach: paymentRequest.attach,
        expireTime,
        payChannel: "wxpay",
        tradeType: "NATIVE",
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        orderId: paymentOrder.id,
        outTradeNo: paymentOrder.outTradeNo,
        amount: paymentOrder.amount,
        codeUrl: paymentOrder.codeUrl,
        qrcodeUrl: paymentOrder.qrcodeUrl,
        expireTime: paymentOrder.expireTime,
      },
    });
  } catch (error) {
    console.error("Payment creation error:", error);
    return NextResponse.json(
      { success: false, message: "创建支付订单失败" },
      { status: 500 },
    );
  }
}

export const runtime = "nodejs";
