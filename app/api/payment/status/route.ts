import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import jwt from "jsonwebtoken";

export async function GET(req: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "未授权访问" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    let userId: string;

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
        userId: string;
      };
      userId = decoded.userId;
    } catch (error) {
      return NextResponse.json(
        { success: false, message: "无效的访问令牌" },
        { status: 401 },
      );
    }

    // 获取订单ID
    const { searchParams } = new URL(req.url);
    const orderId = searchParams.get("orderId");

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: "订单ID不能为空" },
        { status: 400 },
      );
    }

    // 查询订单状态
    const paymentOrder = await prisma.paymentOrder.findFirst({
      where: {
        id: orderId,
        userId,
      },
    });

    if (!paymentOrder) {
      return NextResponse.json(
        { success: false, message: "订单不存在" },
        { status: 404 },
      );
    }

    // 检查订单是否过期
    if (
      paymentOrder.expireTime &&
      new Date() > paymentOrder.expireTime &&
      paymentOrder.status === "PENDING"
    ) {
      // 更新订单状态为过期
      await prisma.paymentOrder.update({
        where: { id: orderId },
        data: { status: "EXPIRED" },
      });

      paymentOrder.status = "EXPIRED";
    }

    return NextResponse.json({
      success: true,
      data: {
        orderId: paymentOrder.id,
        outTradeNo: paymentOrder.outTradeNo,
        amount: paymentOrder.amount,
        status: paymentOrder.status,
        successTime: paymentOrder.successTime,
        expireTime: paymentOrder.expireTime,
      },
    });
  } catch (error) {
    console.error("Payment status query error:", error);
    return NextResponse.json(
      { success: false, message: "查询支付状态失败" },
      { status: 500 },
    );
  }
}

export const runtime = "nodejs";
