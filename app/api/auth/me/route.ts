import { NextRequest, NextResponse } from "next/server";
import { verifyUserTokenWithStatus } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

export async function GET(req: NextRequest) {
  try {
    // 验证token并检查用户状态
    const authResult = await verifyUserTokenWithStatus(req);

    if (authResult.error) {
      const statusCode = authResult.error === "未授权访问" ? 401 : 403;
      return NextResponse.json(
        { error: authResult.error },
        { status: statusCode },
      );
    }

    return NextResponse.json({
      success: true,
      user: authResult.user,
    });
  } catch (error) {
    console.error("Get user info error:", error);
    return NextResponse.json({ error: "获取用户信息失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
