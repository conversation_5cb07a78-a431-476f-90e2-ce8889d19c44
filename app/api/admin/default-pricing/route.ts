import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

// 获取默认定价配置
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    // 获取默认定价配置
    const [defaultInputPrice, defaultOutputPrice] = await Promise.all([
      prisma.systemConfig.findUnique({
        where: { key: "defaultInputTokenPrice" },
      }),
      prisma.systemConfig.findUnique({
        where: { key: "defaultOutputTokenPrice" },
      }),
    ]);

    const defaultPricing = {
      inputTokenPrice: defaultInputPrice
        ? parseFloat(defaultInputPrice.value)
        : 1.0,
      outputTokenPrice: defaultOutputPrice
        ? parseFloat(defaultOutputPrice.value)
        : 2.0,
    };

    console.log(
      `[Admin Default Pricing] Retrieved default pricing for ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      data: defaultPricing,
    });
  } catch (error) {
    console.error("Get default pricing error:", error);
    return NextResponse.json({ error: "获取默认定价失败" }, { status: 500 });
  }
}

// 更新默认定价配置
export async function PUT(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const { inputTokenPrice, outputTokenPrice } = body;

    // 验证价格数据
    if (
      inputTokenPrice !== undefined &&
      (isNaN(inputTokenPrice) || inputTokenPrice < 0)
    ) {
      return NextResponse.json(
        { error: "输入token价格必须是非负数" },
        { status: 400 },
      );
    }

    if (
      outputTokenPrice !== undefined &&
      (isNaN(outputTokenPrice) || outputTokenPrice < 0)
    ) {
      return NextResponse.json(
        { error: "输出token价格必须是非负数" },
        { status: 400 },
      );
    }

    // 更新默认定价配置
    const updatePromises = [];

    if (inputTokenPrice !== undefined) {
      updatePromises.push(
        prisma.systemConfig.upsert({
          where: { key: "defaultInputTokenPrice" },
          update: {
            value: String(inputTokenPrice),
            description: "默认输入Token价格（点数/1K tokens）",
            category: "pricing",
            updatedAt: new Date(),
          },
          create: {
            key: "defaultInputTokenPrice",
            value: String(inputTokenPrice),
            type: "number",
            description: "默认输入Token价格（点数/1K tokens）",
            category: "pricing",
          },
        }),
      );
    }

    if (outputTokenPrice !== undefined) {
      updatePromises.push(
        prisma.systemConfig.upsert({
          where: { key: "defaultOutputTokenPrice" },
          update: {
            value: String(outputTokenPrice),
            description: "默认输出Token价格（点数/1K tokens）",
            category: "pricing",
            updatedAt: new Date(),
          },
          create: {
            key: "defaultOutputTokenPrice",
            value: String(outputTokenPrice),
            type: "number",
            description: "默认输出Token价格（点数/1K tokens）",
            category: "pricing",
          },
        }),
      );
    }

    await Promise.all(updatePromises);

    console.log(
      `[Admin Default Pricing] Updated default pricing by ${tokenData.username}:`,
      {
        inputTokenPrice,
        outputTokenPrice,
      },
    );

    return NextResponse.json({
      success: true,
      message: "默认定价已更新",
      data: {
        inputTokenPrice,
        outputTokenPrice,
      },
    });
  } catch (error) {
    console.error("Update default pricing error:", error);
    return NextResponse.json({ error: "更新默认定价失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
