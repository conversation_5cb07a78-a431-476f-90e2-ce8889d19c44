import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/app/lib/prisma";
import { verifyAdminToken } from "@/app/lib/jwt";

export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const status = searchParams.get("status");
    const userId = searchParams.get("userId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};

    if (status && status !== "ALL") {
      where.status = status;
    }

    if (userId) {
      where.userId = userId;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    // 获取订单列表
    const [orders, total] = await Promise.all([
      prisma.paymentOrder.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.paymentOrder.count({ where }),
    ]);

    // 获取统计数据
    const stats = await prisma.paymentOrder.groupBy({
      by: ["status"],
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
    });

    const statsMap = stats.reduce(
      (acc, stat) => {
        acc[stat.status] = {
          count: stat._count.id,
          amount: stat._sum.amount || 0,
        };
        return acc;
      },
      {} as Record<string, { count: number; amount: number }>,
    );

    // 获取今日统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayStats = await prisma.paymentOrder.groupBy({
      by: ["status"],
      where: {
        createdAt: {
          gte: today,
          lt: tomorrow,
        },
      },
      _count: {
        id: true,
      },
      _sum: {
        amount: true,
      },
    });

    const todayStatsMap = todayStats.reduce(
      (acc, stat) => {
        acc[stat.status] = {
          count: stat._count.id,
          amount: stat._sum.amount || 0,
        };
        return acc;
      },
      {} as Record<string, { count: number; amount: number }>,
    );

    return NextResponse.json({
      success: true,
      data: {
        orders,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats: {
          all: statsMap,
          today: todayStatsMap,
        },
      },
    });
  } catch (error) {
    console.error("Get payments error:", error);
    return NextResponse.json({ error: "获取充值记录失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
