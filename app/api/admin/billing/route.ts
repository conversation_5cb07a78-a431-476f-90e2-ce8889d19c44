import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import {
  getSystemBillingStats,
  DEFAULT_BILLING_PLANS,
} from "@/app/lib/billing-service";

// 获取系统计费统计
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const days = parseInt(searchParams.get("days") || "30");

    // 获取系统计费统计
    const billingStats = await getSystemBillingStats(days);

    console.log(
      `[Admin Billing] Stats requested by ${tokenData.username} for ${days} days`,
    );

    return NextResponse.json({
      success: true,
      data: {
        stats: billingStats,
        plans: DEFAULT_BILLING_PLANS,
        period: {
          days,
          startDate: new Date(
            Date.now() - days * 24 * 60 * 60 * 1000,
          ).toISOString(),
          endDate: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    console.error("Get billing stats error:", error);
    return NextResponse.json({ error: "获取计费统计失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
