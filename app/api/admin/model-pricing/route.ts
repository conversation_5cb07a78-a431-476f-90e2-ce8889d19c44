import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";
import { DEFAULT_MODELS } from "@/app/constant";
import { collectModels } from "@/app/utils/model";
import {
  getEnabledProviders,
  isBackendConfigEnabled,
} from "@/app/lib/provider-service";

// 获取所有模型定价
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    // 获取数据库中的所有定价
    const dbPricings = await prisma.modelPricing.findMany({
      orderBy: [{ provider: "asc" }, { model: "asc" }],
    });

    // 检查是否启用后端配置
    const backendEnabled = await isBackendConfigEnabled();
    let availableModels: any[] = [];

    if (backendEnabled) {
      // 后端模式：从数据库获取启用的服务商和模型
      const enabledProviders = await getEnabledProviders();

      // 获取所有可用模型（包括自定义模型）
      const systemConfig = await prisma.systemConfig.findUnique({
        where: { key: "customModels" },
      });
      const customModels = systemConfig?.value || "";
      const allModels = collectModels(DEFAULT_MODELS, customModels);

      // 过滤出已启用服务商的模型
      availableModels = allModels.filter((model) => {
        if (!model.provider) return false;

        const providerType = model.provider.providerName.toLowerCase();
        const enabledProvider = enabledProviders.find(
          (p) =>
            p.type === providerType ||
            p.name.toLowerCase().includes(providerType),
        );

        if (!enabledProvider) return false;

        // 如果服务商配置了特定模型列表，检查模型是否在列表中
        if (enabledProvider.models && enabledProvider.models.length > 0) {
          return enabledProvider.models.includes(model.name);
        }

        // 否则返回所有模型
        return true;
      });
    } else {
      // 前端模式：返回所有默认模型（为了兼容性）
      availableModels = [...DEFAULT_MODELS];
    }

    // 为每个可用模型创建定价记录（如果不存在）
    const modelPricings = await Promise.all(
      availableModels.map(async (model) => {
        // 查找现有定价
        let pricing = dbPricings.find((p) => p.model === model.name);

        if (!pricing) {
          // 如果没有定价记录，使用系统默认定价创建记录
          const { getDefaultPricing } = await import(
            "@/app/lib/api-log-service"
          );
          const defaultPricing = await getDefaultPricing();

          pricing = await prisma.modelPricing.create({
            data: {
              model: model.name,
              provider: model.provider?.providerName.toLowerCase() || "unknown",
              inputTokenPrice: defaultPricing.input,
              outputTokenPrice: defaultPricing.output,
              description: `${model.provider?.providerName} ${model.name} 模型定价`,
              enabled: true,
            },
          });
        }

        return {
          id: pricing.id,
          model: model.name,
          displayName: (model as any).displayName || model.name,
          provider: model.provider?.providerName || "unknown",
          providerDisplayName: model.provider?.providerName || "Unknown",
          inputTokenPrice: pricing.inputTokenPrice,
          outputTokenPrice: pricing.outputTokenPrice,
          enabled: pricing.enabled,
          description: pricing.description,
          createdAt: pricing.createdAt,
          updatedAt: pricing.updatedAt,
        };
      }),
    );

    console.log(
      `[Admin Model Pricing] Retrieved ${modelPricings.length} model pricings for ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      data: modelPricings,
    });
  } catch (error) {
    console.error("Get model pricing error:", error);
    return NextResponse.json({ error: "获取模型定价失败" }, { status: 500 });
  }
}

// 批量更新模型定价
export async function PUT(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const { pricings } = body;

    if (!Array.isArray(pricings)) {
      return NextResponse.json(
        { error: "无效的定价数据格式" },
        { status: 400 },
      );
    }

    // 批量更新定价
    const updatePromises = pricings.map(async (pricing: any) => {
      const { id, inputTokenPrice, outputTokenPrice, enabled, description } =
        pricing;

      return prisma.modelPricing.update({
        where: { id },
        data: {
          inputTokenPrice: parseFloat(inputTokenPrice),
          outputTokenPrice: parseFloat(outputTokenPrice),
          enabled: Boolean(enabled),
          description: description || undefined,
        },
      });
    });

    const updatedPricings = await Promise.all(updatePromises);

    console.log(
      `[Admin Model Pricing] Updated ${updatedPricings.length} model pricings by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      message: `已更新 ${updatedPricings.length} 个模型的定价`,
      data: updatedPricings,
    });
  } catch (error) {
    console.error("Update model pricing error:", error);
    return NextResponse.json({ error: "更新模型定价失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
