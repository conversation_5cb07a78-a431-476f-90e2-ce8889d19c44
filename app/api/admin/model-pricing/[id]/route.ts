import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

interface RouteParams {
  params: {
    id: string;
  };
}

// 获取单个模型定价
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const pricing = await prisma.modelPricing.findUnique({
      where: { id: params.id },
    });

    if (!pricing) {
      return NextResponse.json({ error: "模型定价不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: pricing,
    });
  } catch (error) {
    console.error("Get model pricing error:", error);
    return NextResponse.json({ error: "获取模型定价失败" }, { status: 500 });
  }
}

// 更新单个模型定价
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const { inputTokenPrice, outputTokenPrice, enabled, description } = body;

    // 验证价格数据
    if (
      inputTokenPrice !== undefined &&
      (isNaN(inputTokenPrice) || inputTokenPrice < 0)
    ) {
      return NextResponse.json(
        { error: "输入token价格必须是非负数" },
        { status: 400 },
      );
    }

    if (
      outputTokenPrice !== undefined &&
      (isNaN(outputTokenPrice) || outputTokenPrice < 0)
    ) {
      return NextResponse.json(
        { error: "输出token价格必须是非负数" },
        { status: 400 },
      );
    }

    const updateData: any = {};
    if (inputTokenPrice !== undefined)
      updateData.inputTokenPrice = parseFloat(inputTokenPrice);
    if (outputTokenPrice !== undefined)
      updateData.outputTokenPrice = parseFloat(outputTokenPrice);
    if (enabled !== undefined) updateData.enabled = Boolean(enabled);
    if (description !== undefined) updateData.description = description;

    const updatedPricing = await prisma.modelPricing.update({
      where: { id: params.id },
      data: updateData,
    });

    console.log(
      `[Admin Model Pricing] Updated pricing for model ${updatedPricing.model} by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      message: "模型定价已更新",
      data: updatedPricing,
    });
  } catch (error) {
    console.error("Update model pricing error:", error);
    if (
      error &&
      typeof error === "object" &&
      "code" in error &&
      error.code === "P2025"
    ) {
      return NextResponse.json({ error: "模型定价不存在" }, { status: 404 });
    }
    return NextResponse.json({ error: "更新模型定价失败" }, { status: 500 });
  }
}

// 删除单个模型定价
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const deletedPricing = await prisma.modelPricing.delete({
      where: { id: params.id },
    });

    console.log(
      `[Admin Model Pricing] Deleted pricing for model ${deletedPricing.model} by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      message: "模型定价已删除",
    });
  } catch (error) {
    console.error("Delete model pricing error:", error);
    if (
      error &&
      typeof error === "object" &&
      "code" in error &&
      error.code === "P2025"
    ) {
      return NextResponse.json({ error: "模型定价不存在" }, { status: 404 });
    }
    return NextResponse.json({ error: "删除模型定价失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
