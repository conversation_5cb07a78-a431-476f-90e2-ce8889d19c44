import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

// 获取服务商列表
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { type: { contains: search } },
      ];
    }

    // 获取服务商列表和总数
    const [providers, total] = await Promise.all([
      prisma.provider.findMany({
        where,
        select: {
          id: true,
          name: true,
          type: true,
          baseUrl: true,
          enabled: true,
          description: true,
          createdAt: true,
          updatedAt: true,
          // 不返回API Key
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.provider.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      providers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get providers error:", error);
    return NextResponse.json({ error: "获取服务商列表失败" }, { status: 500 });
  }
}

// 创建服务商
export async function POST(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const body = await req.json();
    const {
      name,
      type,
      baseUrl,
      apiKey,
      enabled = true,
      description,
      models,
      config,
    } = body;

    // 验证必需字段
    if (!name || !type || !baseUrl || !apiKey) {
      return NextResponse.json(
        { error: "缺少必需字段: name, type, baseUrl, apiKey" },
        { status: 400 },
      );
    }

    // 检查服务商名称是否已存在
    const existingProvider = await prisma.provider.findFirst({
      where: { name: name },
    });

    if (existingProvider) {
      return NextResponse.json({ error: "服务商名称已存在" }, { status: 409 });
    }

    // 创建服务商（这里应该加密API Key，但为了简化暂时不加密）
    const provider = await prisma.provider.create({
      data: {
        name,
        type,
        baseUrl,
        apiKey, // 实际应用中应该加密存储
        enabled,
        description,
        models: models ? JSON.stringify(models) : null,
        config: config ? JSON.stringify(config) : null,
      },
      select: {
        id: true,
        name: true,
        type: true,
        baseUrl: true,
        enabled: true,
        description: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log(
      `[Admin] Created provider: ${provider.name} (${provider.type}) by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      provider,
    });
  } catch (error) {
    console.error("Create provider error:", error);
    return NextResponse.json({ error: "创建服务商失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
