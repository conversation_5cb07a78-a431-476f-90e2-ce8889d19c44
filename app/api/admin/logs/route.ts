import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { getApiLogs, getApiStats } from "@/app/lib/api-log-service";

// 获取API调用日志
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const userId = searchParams.get("userId") || undefined;
    const providerId = searchParams.get("providerId") || undefined;
    const status = searchParams.get("status") || undefined;
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    const offset = (page - 1) * limit;

    // 构建查询参数
    const query: any = {
      limit,
      offset,
    };

    if (userId) query.userId = userId;
    if (providerId) query.providerId = providerId;
    if (status) query.status = status;
    if (startDate) query.startDate = new Date(startDate);
    if (endDate) query.endDate = new Date(endDate);

    // 获取日志数据
    const logs = await getApiLogs(query);

    // 获取统计信息
    const statsQuery: any = {};
    if (userId) statsQuery.userId = userId;
    if (providerId) statsQuery.providerId = providerId;
    if (status) statsQuery.status = status;
    if (startDate) statsQuery.startDate = new Date(startDate);
    if (endDate) statsQuery.endDate = new Date(endDate);

    const stats = await getApiStats(statsQuery);

    // 获取总数（用于分页）
    const { prisma } = await import("@/app/lib/prisma");
    const where: any = {};

    if (userId) where.userId = userId;
    if (providerId) where.providerId = providerId;
    if (status) where.status = status;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    const total = await prisma.apiLog.count({ where });

    console.log(
      `[Admin] Logs requested by ${tokenData.username}, page ${page}, filters:`,
      {
        userId,
        providerId,
        status,
        startDate,
        endDate,
      },
    );

    return NextResponse.json({
      success: true,
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      stats,
    });
  } catch (error) {
    console.error("Get logs error:", error);
    return NextResponse.json({ error: "获取日志数据失败" }, { status: 500 });
  }
}

// 清理过期日志
export async function DELETE(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const daysToKeep = parseInt(searchParams.get("daysToKeep") || "90");

    if (daysToKeep < 1) {
      return NextResponse.json({ error: "保留天数必须大于0" }, { status: 400 });
    }

    const { cleanupOldLogs } = await import("@/app/lib/api-log-service");
    const deletedCount = await cleanupOldLogs(daysToKeep);

    console.log(
      `[Admin] Cleaned up ${deletedCount} old logs (keeping ${daysToKeep} days) by ${tokenData.username}`,
    );

    return NextResponse.json({
      success: true,
      message: `已清理 ${deletedCount} 条过期日志`,
      deletedCount,
    });
  } catch (error) {
    console.error("Cleanup logs error:", error);
    return NextResponse.json({ error: "清理日志失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
