import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { prisma } from "@/app/lib/prisma";

interface RouteParams {
  params: {
    id: string;
  };
}

// 重置用户月度统计
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const userId = params.id;

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 重置用户月度统计
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        monthlyTokensUsed: 0,
        monthlyCost: 0.0,
        lastResetAt: new Date(),
      },
      select: {
        id: true,
        username: true,
        monthlyTokensUsed: true,
        monthlyCost: true,
        lastResetAt: true,
      },
    });

    return NextResponse.json({
      success: true,
      message: "用户月度统计已重置",
      user: updatedUser,
    });
  } catch (error) {
    console.error("重置用户统计失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
