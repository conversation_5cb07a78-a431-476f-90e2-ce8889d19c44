import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/jwt";
import { getApiStats } from "@/app/lib/api-log-service";
import { prisma } from "@/app/lib/prisma";

// 获取系统统计数据
export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const tokenData = verifyAdminToken(req);
    if (!tokenData) {
      return NextResponse.json({ error: "需要管理员权限" }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const days = parseInt(searchParams.get("days") || "30");

    // 计算日期范围
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 获取基础统计
    const [
      totalUsers,
      totalProviders,
      enabledProviders,
      apiStats,
      recentUsers,
      topProviders,
      totalCostStats,
      topUsers,
      topModels,
    ] = await Promise.all([
      // 总用户数
      prisma.user.count(),

      // 总服务商数
      prisma.provider.count(),

      // 启用的服务商数
      prisma.provider.count({
        where: { enabled: true },
      }),

      // API调用统计
      getApiStats({
        startDate,
      }),

      // 最近注册用户数
      prisma.user.count({
        where: {
          createdAt: {
            gte: startDate,
          },
        },
      }),

      // 最活跃的服务商
      prisma.apiLog.groupBy({
        by: ["providerId"],
        where: {
          createdAt: {
            gte: startDate,
          },
        },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: "desc",
          },
        },
        take: 5,
      }),

      // 总成本统计
      prisma.user.aggregate({
        _sum: {
          totalCost: true,
          monthlyTokensUsed: true,
          monthlyCost: true,
        },
      }),

      // 用户使用排行
      prisma.user.findMany({
        select: {
          id: true,
          username: true,
          totalTokensUsed: true,
          totalCost: true,
          monthlyTokensUsed: true,
          monthlyCost: true,
          _count: {
            select: {
              apiLogs: {
                where: { createdAt: { gte: startDate } },
              },
            },
          },
        },
        orderBy: {
          totalCost: "desc",
        },
        take: 10,
      }),

      // 模型使用排行
      prisma.apiLog.groupBy({
        by: ["model"],
        where: { createdAt: { gte: startDate } },
        _count: {
          id: true,
        },
        _sum: {
          tokensUsed: true,
          cost: true,
        },
        orderBy: {
          _sum: {
            cost: "desc",
          },
        },
        take: 10,
      }),
    ]);

    // 获取服务商详细信息
    const providerIds = topProviders.map((p) => p.providerId);
    const providerDetails = await prisma.provider.findMany({
      where: {
        id: {
          in: providerIds,
        },
      },
      select: {
        id: true,
        name: true,
        type: true,
      },
    });

    const topProvidersWithDetails = topProviders.map((p) => {
      const provider = providerDetails.find((pd) => pd.id === p.providerId);
      return {
        ...p,
        provider: provider || { name: "Unknown", type: "unknown" },
      };
    });

    // 获取每日API调用趋势（最近7天）
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split("T")[0];
    }).reverse();

    const dailyStats = await Promise.all(
      last7Days.map(async (date) => {
        const startOfDay = new Date(date + "T00:00:00.000Z");
        const endOfDay = new Date(date + "T23:59:59.999Z");

        const [calls, successCalls] = await Promise.all([
          prisma.apiLog.count({
            where: {
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
          }),
          prisma.apiLog.count({
            where: {
              createdAt: {
                gte: startOfDay,
                lte: endOfDay,
              },
              status: "success",
            },
          }),
        ]);

        return {
          date,
          calls,
          successCalls,
          successRate:
            calls > 0 ? ((successCalls / calls) * 100).toFixed(1) : "0",
        };
      }),
    );

    console.log(
      `[Admin] Stats requested by ${tokenData.username} for ${days} days`,
    );

    return NextResponse.json({
      success: true,
      stats: {
        overview: {
          totalUsers,
          totalProviders,
          enabledProviders,
          recentUsers,
          userTotalCost: totalCostStats._sum.totalCost || 0,
          monthlyTokens: totalCostStats._sum.monthlyTokensUsed || 0,
          monthlyCost: totalCostStats._sum.monthlyCost || 0,
          ...apiStats,
        },
        topProviders: topProvidersWithDetails,
        topUsers,
        topModels,
        dailyTrend: dailyStats,
      },
    });
  } catch (error) {
    console.error("Get stats error:", error);
    return NextResponse.json({ error: "获取统计数据失败" }, { status: 500 });
  }
}

export const runtime = "nodejs";
