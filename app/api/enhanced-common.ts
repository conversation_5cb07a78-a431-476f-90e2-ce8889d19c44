import { NextRequest, NextResponse } from "next/server";
import { OPENAI_BASE_URL } from "../constant";
import { cloudflareAIGatewayUrl } from "../utils/cloudflare";
import { logEnhancedApiCall } from "../lib/api-log-service";
import { AuthResult } from "./enhanced-auth";

export interface RequestOptions {
  authResult: AuthResult;
  req: NextRequest;
  providerType?: string;
  endpoint?: string;
}

/**
 * 增强的请求处理函数，支持后端配置和日志记录
 */
export async function enhancedRequest(options: RequestOptions) {
  const {
    authResult,
    req,
    providerType = "openai",
    endpoint = "chat/completions",
  } = options;
  const startTime = Date.now();
  const controller = new AbortController();

  const isAzure = req.nextUrl.pathname.includes("azure/deployments");

  // 确定认证头
  let authValue: string;
  let authHeaderName: string;

  if (authResult.backendMode) {
    // 后端模式：使用后端配置的API Key
    authValue = `Bearer ${authResult.apiKey}`;
    authHeaderName = isAzure ? "api-key" : "Authorization";
  } else {
    // 前端模式：使用用户提供的API Key
    if (isAzure) {
      authValue =
        req.headers
          .get("Authorization")
          ?.trim()
          .replaceAll("Bearer ", "")
          .trim() ?? "";
      authHeaderName = "api-key";
    } else {
      authValue = req.headers.get("Authorization") ?? "";
      authHeaderName = "Authorization";
    }
  }

  // 确定请求路径
  let path = `${req.nextUrl.pathname}`.replaceAll(`/api/${providerType}/`, "");

  // 确定基础URL
  let baseUrl: string;
  if (authResult.backendMode && authResult.baseUrl) {
    baseUrl = authResult.baseUrl;
  } else {
    baseUrl = OPENAI_BASE_URL;
  }

  if (!baseUrl.startsWith("http")) {
    baseUrl = `https://${baseUrl}`;
  }

  if (baseUrl.endsWith("/")) {
    baseUrl = baseUrl.slice(0, -1);
  }

  console.log("[Enhanced Proxy] ", path);
  console.log("[Base Url]", baseUrl);
  console.log("[Backend Mode]", authResult.backendMode);

  // 设置超时
  const timeoutId = setTimeout(
    () => {
      controller.abort();
    },
    10 * 60 * 1000,
  );

  // Azure特殊处理
  if (isAzure) {
    const azureApiVersion =
      req?.nextUrl?.searchParams?.get("api-version") || "2024-02-01";
    baseUrl = baseUrl.split("/deployments").shift() as string;
    path = `${req.nextUrl.pathname.replaceAll(
      "/api/azure/",
      "",
    )}?api-version=${azureApiVersion}`;
  }

  const fetchUrl = cloudflareAIGatewayUrl(`${baseUrl}/${path}`);
  console.log("fetchUrl", fetchUrl);

  const fetchOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "no-store",
      [authHeaderName]: authValue,
    },
    method: req.method,
    body: req.body,
    redirect: "manual",
    // @ts-ignore
    duplex: "half",
    signal: controller.signal,
  };

  try {
    const res = await fetch(fetchUrl, fetchOptions);
    const duration = Date.now() - startTime;

    // 记录API调用日志（仅在后端模式下）
    if (authResult.backendMode && authResult.providerId) {
      const status = res.ok ? "success" : "error";
      const errorMsg = res.ok
        ? undefined
        : `HTTP ${res.status}: ${res.statusText}`;

      // 获取请求体用于日志记录
      let requestBody: any;
      try {
        if (req.body) {
          const bodyText = await req.text();
          requestBody = JSON.parse(bodyText);
          // 重新创建请求体，因为已经被读取了
          fetchOptions.body = bodyText;
        }
      } catch (e) {
        // 忽略解析错误
      }

      // 获取响应体用于日志记录（仅非流式响应）
      let responseBody: any;
      if (
        res.ok &&
        !res.headers.get("content-type")?.includes("text/event-stream")
      ) {
        try {
          const clonedRes = res.clone();
          responseBody = await clonedRes.json();
        } catch (e) {
          // 忽略解析错误，可能是流式响应或其他格式
        }
      }

      // 使用增强的日志记录功能
      await logEnhancedApiCall({
        userId: authResult.userId,
        providerId: authResult.providerId,
        endpoint: `/${endpoint}`,
        method: req.method,
        requestBody,
        responseBody,
        status,
        errorMsg,
        duration,
      });
    }

    // 处理响应头
    const newHeaders = new Headers(res.headers);
    newHeaders.delete("www-authenticate");
    newHeaders.set("X-Accel-Buffering", "no");
    newHeaders.delete("OpenAI-Organization");
    newHeaders.delete("content-encoding");

    clearTimeout(timeoutId);

    return new Response(res.body, {
      status: res.status,
      statusText: res.statusText,
      headers: newHeaders,
    });
  } catch (error) {
    const duration = Date.now() - startTime;

    console.error("[Enhanced Request] Error:", error);

    // 记录错误日志（仅在后端模式下）
    if (authResult.backendMode && authResult.providerId) {
      // 获取请求体用于日志记录
      let requestBody: any;
      try {
        if (req.body) {
          const bodyText = await req.text();
          requestBody = JSON.parse(bodyText);
        }
      } catch (e) {
        // 忽略解析错误
      }

      await logEnhancedApiCall({
        userId: authResult.userId,
        providerId: authResult.providerId,
        endpoint: `/${endpoint}`,
        method: req.method,
        requestBody,
        status: "error",
        errorMsg: error instanceof Error ? error.message : "Unknown error",
        duration,
      });
    }

    clearTimeout(timeoutId);

    return NextResponse.json(
      {
        error: "Request failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * 向后兼容的请求函数
 */
export async function requestOpenai(req: NextRequest) {
  const controller = new AbortController();

  const isAzure = req.nextUrl.pathname.includes("azure/deployments");

  var authValue,
    authHeaderName = "";
  if (isAzure) {
    authValue =
      req.headers
        .get("Authorization")
        ?.trim()
        .replaceAll("Bearer ", "")
        .trim() ?? "";

    authHeaderName = "api-key";
  } else {
    authValue = req.headers.get("Authorization") ?? "";
    authHeaderName = "Authorization";
  }

  let path = `${req.nextUrl.pathname}`.replaceAll("/api/openai/", "");

  let baseUrl = OPENAI_BASE_URL;

  if (!baseUrl.startsWith("http")) {
    baseUrl = `https://${baseUrl}`;
  }

  if (baseUrl.endsWith("/")) {
    baseUrl = baseUrl.slice(0, -1);
  }

  console.log("[Proxy] ", path);
  console.log("[Base Url]", baseUrl);

  const timeoutId = setTimeout(
    () => {
      controller.abort();
    },
    10 * 60 * 1000,
  );

  if (isAzure) {
    const azureApiVersion =
      req?.nextUrl?.searchParams?.get("api-version") || "2024-02-01";
    baseUrl = baseUrl.split("/deployments").shift() as string;
    path = `${req.nextUrl.pathname.replaceAll(
      "/api/azure/",
      "",
    )}?api-version=${azureApiVersion}`;
  }

  const fetchUrl = cloudflareAIGatewayUrl(`${baseUrl}/${path}`);
  console.log("fetchUrl", fetchUrl);
  const fetchOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "no-store",
      [authHeaderName]: authValue,
    },
    method: req.method,
    body: req.body,
    redirect: "manual",
    // @ts-ignore
    duplex: "half",
    signal: controller.signal,
  };

  try {
    const res = await fetch(fetchUrl, fetchOptions);

    const newHeaders = new Headers(res.headers);
    newHeaders.delete("www-authenticate");
    newHeaders.set("X-Accel-Buffering", "no");
    newHeaders.delete("OpenAI-Organization");
    newHeaders.delete("content-encoding");

    return new Response(res.body, {
      status: res.status,
      statusText: res.statusText,
      headers: newHeaders,
    });
  } catch (e) {
    console.error("[OpenAI] ", e);
    return NextResponse.json({
      error: e instanceof Error ? e.message : "Unknown error",
    });
  } finally {
    clearTimeout(timeoutId);
  }
}
