import React, { useState, useEffect } from "react";
import { useAccessStore } from "@/app/store/access";
import { useSystemConfigStore } from "@/app/store/system-config";
import { useUserStore } from "@/app/store/user";
import { ServiceProvider } from "@/app/constant";
import { ModelProviderIcon } from "@/app/components/provider-icon";
import { showToast } from "@/app/components/ui-lib";
import { ModelManager } from "@/app/components/model-manager";
import DownIcon from "@/app/icons/down.svg";
import styles from "./providers-management.module.scss";

interface ProviderInfo {
  id: ServiceProvider;
  name: string;
  type: string;
  description: string;
  hasApiKey: boolean;
  enabled: boolean;
  models?: string[];
}

const BUILTIN_PROVIDERS = [
  {
    id: ServiceProvider.OpenAI,
    name: "OpenAI",
    type: "openai",
    description: "OpenAI GPT 系列模型，包括 GPT-4、GPT-3.5 等",
    baseUrl: "https://api.openai.com",
  },
  {
    id: ServiceProvider.Azure,
    name: "Azure OpenAI",
    type: "azure",
    description: "微软 Azure OpenAI 服务",
    baseUrl: "https://your-resource.openai.azure.com",
  },
  {
    id: ServiceProvider.Google,
    name: "Google",
    type: "google",
    description: "Google Gemini 系列模型",
    baseUrl: "https://generativelanguage.googleapis.com",
  },
  {
    id: ServiceProvider.Anthropic,
    name: "Anthropic",
    type: "anthropic",
    description: "Anthropic Claude 系列模型",
    baseUrl: "https://api.anthropic.com",
  },
  {
    id: ServiceProvider.ByteDance,
    name: "字节跳动",
    type: "bytedance",
    description: "字节跳动豆包系列模型",
    baseUrl: "https://ark.cn-beijing.volces.com",
  },
  {
    id: ServiceProvider.Alibaba,
    name: "阿里云",
    type: "alibaba",
    description: "阿里云通义千问系列模型",
    baseUrl: "https://dashscope.aliyuncs.com",
  },
  {
    id: ServiceProvider.Moonshot,
    name: "月之暗面",
    type: "moonshot",
    description: "Moonshot Kimi 系列模型",
    baseUrl: "https://api.moonshot.cn",
  },
  {
    id: ServiceProvider.DeepSeek,
    name: "DeepSeek",
    type: "deepseek",
    description: "DeepSeek 系列模型",
    baseUrl: "https://api.deepseek.com",
  },
  {
    id: ServiceProvider.XAI,
    name: "xAI",
    type: "xai",
    description: "xAI Grok 系列模型",
    baseUrl: "https://api.x.ai",
  },
  {
    id: ServiceProvider.SiliconFlow,
    name: "SiliconFlow",
    type: "siliconflow",
    description: "SiliconFlow 硅基流动",
    baseUrl: "https://api.siliconflow.cn",
  },
];

export function ProvidersManagement() {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const [providers, setProviders] = useState<ProviderInfo[]>([]);
  const [collapsedProviders, setCollapsedProviders] = useState<
    Record<ServiceProvider, boolean>
  >({
    [ServiceProvider.OpenAI]: true,
    [ServiceProvider.Azure]: true,
    [ServiceProvider.Google]: true,
    [ServiceProvider.Anthropic]: true,
    [ServiceProvider.ByteDance]: true,
    [ServiceProvider.Alibaba]: true,
    [ServiceProvider.Moonshot]: true,
    [ServiceProvider.XAI]: true,
    [ServiceProvider.DeepSeek]: true,
    [ServiceProvider.SiliconFlow]: true,
  });
  const [formData, setFormData] = useState<
    Record<
      ServiceProvider,
      {
        apiKey: string;
        baseUrl: string;
      }
    >
  >({} as any);
  const [showModelManager, setShowModelManager] = useState(false);
  const [currentProvider, setCurrentProvider] =
    useState<ServiceProvider | null>(null);

  useEffect(() => {
    loadProviders();
  }, []);

  const getProviderApiKey = (provider: ServiceProvider): string => {
    switch (provider) {
      case ServiceProvider.OpenAI:
        return accessStore.openaiApiKey;
      case ServiceProvider.Azure:
        return accessStore.azureApiKey;
      case ServiceProvider.Google:
        return accessStore.googleApiKey;
      case ServiceProvider.Anthropic:
        return accessStore.anthropicApiKey;
      case ServiceProvider.ByteDance:
        return accessStore.bytedanceApiKey;
      case ServiceProvider.Alibaba:
        return accessStore.alibabaApiKey;
      case ServiceProvider.Moonshot:
        return accessStore.moonshotApiKey;
      case ServiceProvider.DeepSeek:
        return accessStore.deepseekApiKey;
      case ServiceProvider.XAI:
        return accessStore.xaiApiKey;
      case ServiceProvider.SiliconFlow:
        return accessStore.siliconflowApiKey;
      default:
        return "";
    }
  };

  const getProviderBaseUrl = (provider: ServiceProvider): string => {
    switch (provider) {
      case ServiceProvider.OpenAI:
        return accessStore.openaiUrl;
      case ServiceProvider.Azure:
        return accessStore.azureUrl;
      case ServiceProvider.Google:
        return accessStore.googleUrl;
      case ServiceProvider.Anthropic:
        return accessStore.anthropicUrl;
      case ServiceProvider.ByteDance:
        return accessStore.bytedanceUrl;
      case ServiceProvider.Alibaba:
        return accessStore.alibabaUrl;
      case ServiceProvider.Moonshot:
        return accessStore.moonshotUrl;
      case ServiceProvider.DeepSeek:
        return accessStore.deepseekUrl;
      case ServiceProvider.XAI:
        return accessStore.xaiUrl;
      case ServiceProvider.SiliconFlow:
        return accessStore.siliconflowUrl;
      default:
        return "";
    }
  };

  const updateProviderApiKey = (provider: ServiceProvider, apiKey: string) => {
    accessStore.update((access) => {
      switch (provider) {
        case ServiceProvider.OpenAI:
          access.openaiApiKey = apiKey;
          break;
        case ServiceProvider.Azure:
          access.azureApiKey = apiKey;
          break;
        case ServiceProvider.Google:
          access.googleApiKey = apiKey;
          break;
        case ServiceProvider.Anthropic:
          access.anthropicApiKey = apiKey;
          break;
        case ServiceProvider.ByteDance:
          access.bytedanceApiKey = apiKey;
          break;
        case ServiceProvider.Alibaba:
          access.alibabaApiKey = apiKey;
          break;
        case ServiceProvider.Moonshot:
          access.moonshotApiKey = apiKey;
          break;
        case ServiceProvider.DeepSeek:
          access.deepseekApiKey = apiKey;
          break;
        case ServiceProvider.XAI:
          access.xaiApiKey = apiKey;
          break;
        case ServiceProvider.SiliconFlow:
          access.siliconflowApiKey = apiKey;
          break;
      }
    });
  };

  const updateProviderBaseUrl = (
    provider: ServiceProvider,
    baseUrl: string,
  ) => {
    accessStore.update((access) => {
      switch (provider) {
        case ServiceProvider.OpenAI:
          access.openaiUrl = baseUrl;
          break;
        case ServiceProvider.Azure:
          access.azureUrl = baseUrl;
          break;
        case ServiceProvider.Google:
          access.googleUrl = baseUrl;
          break;
        case ServiceProvider.Anthropic:
          access.anthropicUrl = baseUrl;
          break;
        case ServiceProvider.ByteDance:
          access.bytedanceUrl = baseUrl;
          break;
        case ServiceProvider.Alibaba:
          access.alibabaUrl = baseUrl;
          break;
        case ServiceProvider.Moonshot:
          access.moonshotUrl = baseUrl;
          break;
        case ServiceProvider.DeepSeek:
          access.deepseekUrl = baseUrl;
          break;
        case ServiceProvider.XAI:
          access.xaiUrl = baseUrl;
          break;
        case ServiceProvider.SiliconFlow:
          access.siliconflowUrl = baseUrl;
          break;
      }
    });
  };

  const loadProviders = () => {
    const providerList: ProviderInfo[] = BUILTIN_PROVIDERS.map((provider) => {
      const hasApiKey = !!getProviderApiKey(provider.id);
      const enabled = accessStore.enabledProviders?.[provider.id] || false;
      // 从 enabledModels 中获取该服务商的启用模型列表
      const enabledModels = accessStore.enabledModels?.[provider.id] || [];
      return {
        id: provider.id,
        name: provider.name,
        type: provider.type,
        description: provider.description,
        hasApiKey,
        enabled,
        models: enabledModels,
      };
    });

    setProviders(providerList);

    // 初始化表单数据
    const initialFormData: Record<
      ServiceProvider,
      { apiKey: string; baseUrl: string }
    > = {} as any;
    BUILTIN_PROVIDERS.forEach((provider) => {
      initialFormData[provider.id] = {
        apiKey: getProviderApiKey(provider.id) || "",
        baseUrl: getProviderBaseUrl(provider.id) || "",
      };
    });
    setFormData(initialFormData);
  };

  const handleToggleProvider = async (
    providerId: ServiceProvider,
    enabled: boolean,
  ) => {
    try {
      // 更新前端存储
      accessStore.update((access) => {
        access.enabledProviders[providerId] = enabled;
      });

      // 如果启用服务商，需要确保数据库中有对应的服务商配置
      if (enabled) {
        const provider = BUILTIN_PROVIDERS.find((p) => p.id === providerId);
        const apiKey = getProviderApiKey(providerId);
        const baseUrl = getProviderBaseUrl(providerId);

        if (!provider || !apiKey) {
          // 如果没有API密钥，只更新前端状态，用户需要先配置API密钥
          loadProviders();
          return;
        }

        // 检查数据库中是否已存在该服务商
        const existingResponse = await fetch("/api/providers", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userStore.token}`,
          },
        });

        if (existingResponse.ok) {
          const existingData = await existingResponse.json();
          const existingProvider = existingData.providers?.find(
            (p: any) => p.type === provider.type,
          );

          if (existingProvider) {
            // 更新现有服务商的启用状态
            await fetch(`/api/providers/${existingProvider.id}`, {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${userStore.token}`,
              },
              body: JSON.stringify({
                ...existingProvider,
                enabled: enabled,
              }),
            });
          }
        }

        // 刷新系统配置
        const systemConfigStore = useSystemConfigStore.getState();
        await systemConfigStore.refreshConfig();
      }

      loadProviders();
    } catch (error) {
      console.error("切换服务商状态失败:", error);
      // 回滚前端状态
      accessStore.update((access) => {
        access.enabledProviders[providerId] = !enabled;
      });
      loadProviders();
    }
  };

  const handleToggleCollapse = (providerId: ServiceProvider) => {
    setCollapsedProviders((prev) => ({
      ...prev,
      [providerId]: !prev[providerId],
    }));
  };

  const handleFormChange = (
    providerId: ServiceProvider,
    field: "apiKey" | "baseUrl",
    value: string,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [providerId]: {
        ...prev[providerId],
        [field]: value,
      },
    }));
  };

  const handleSave = async (providerId: ServiceProvider) => {
    try {
      const data = formData[providerId];
      const provider = BUILTIN_PROVIDERS.find((p) => p.id === providerId);

      if (!provider) {
        throw new Error("未找到服务商配置");
      }

      if (!data.apiKey.trim()) {
        showToast("请输入API密钥");
        return;
      }

      // 准备服务商数据
      const providerData = {
        name: provider.name,
        type: provider.type,
        baseUrl: data.baseUrl.trim() || provider.baseUrl,
        apiKey: data.apiKey.trim(),
        enabled: accessStore.enabledProviders?.[providerId] || false,
        models: accessStore.enabledModels?.[providerId] || [],
        description: provider.description,
      };

      // 检查服务商是否已存在
      const existingResponse = await fetch("/api/providers", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userStore.token}`,
        },
      });

      if (!existingResponse.ok) {
        throw new Error("获取服务商列表失败");
      }

      const existingData = await existingResponse.json();
      const existingProvider = existingData.providers?.find(
        (p: any) => p.type === provider.type,
      );

      let response;
      if (existingProvider) {
        // 更新现有服务商
        response = await fetch(`/api/providers/${existingProvider.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userStore.token}`,
          },
          body: JSON.stringify(providerData),
        });
      } else {
        // 创建新服务商
        response = await fetch("/api/providers", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userStore.token}`,
          },
          body: JSON.stringify(providerData),
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "保存失败");
      }

      // 同时保存到前端存储（保持兼容性）
      if (data.apiKey.trim()) {
        updateProviderApiKey(providerId, data.apiKey.trim());
      }
      if (data.baseUrl.trim()) {
        updateProviderBaseUrl(providerId, data.baseUrl.trim());
      }

      showToast("配置保存成功");

      // 刷新系统配置以更新可用服务商列表
      const systemConfigStore = useSystemConfigStore.getState();
      await systemConfigStore.refreshConfig();

      // 重新加载服务商列表，确保显示最新的模型信息
      loadProviders();
    } catch (error) {
      console.error("保存配置失败:", error);
      showToast(
        `保存配置失败: ${error instanceof Error ? error.message : "未知错误"}`,
      );
    }
  };

  const handleManageModels = (provider: ProviderInfo) => {
    setCurrentProvider(provider.id);
    setShowModelManager(true);
  };

  return (
    <div className={styles["providers-management"]}>
      <div className={styles["page-header"]}>
        <h1>服务商管理</h1>
        <p>管理和配置各个AI服务提供商的API密钥和设置</p>
      </div>

      <div className={styles["provider-cards"]}>
        {providers.map((provider) => {
          const isCollapsed = collapsedProviders[provider.id];
          const currentFormData = formData[provider.id] || {
            apiKey: "",
            baseUrl: "",
          };

          return (
            <div
              key={provider.id}
              className={`${styles["provider-card"]} ${
                provider.enabled ? styles["provider-card-active"] : ""
              }`}
            >
              <div
                className={styles["provider-card-header"]}
                onClick={() => {
                  if (provider.enabled) {
                    handleToggleCollapse(provider.id);
                  }
                }}
              >
                <div className={styles["provider-info"]}>
                  <div className={styles["provider-icon"]}>
                    <ModelProviderIcon provider={provider.id} size={24} />
                  </div>
                  <div className={styles["provider-name-container"]}>
                    <h3 className={styles["provider-name"]}>{provider.name}</h3>
                    {provider.enabled && (
                      <span className={styles["provider-badge"]}>已启用</span>
                    )}
                  </div>
                  <div className={styles["provider-description"]}>
                    {provider.description}
                  </div>
                </div>
                <div className={styles["provider-controls"]}>
                  <div className={styles["provider-toggle"]}>
                    <input
                      type="checkbox"
                      checked={provider.enabled}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleToggleProvider(provider.id, e.target.checked);
                      }}
                      className={styles["provider-checkbox"]}
                    />
                  </div>
                  {provider.enabled && (
                    <button
                      className={`${styles["collapse-button"]} ${
                        isCollapsed ? styles["collapsed"] : ""
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleCollapse(provider.id);
                      }}
                    >
                      <DownIcon />
                    </button>
                  )}
                </div>
              </div>

              {provider.enabled && (
                <div
                  className={`${styles["provider-config"]} ${
                    isCollapsed ? styles["collapsed"] : styles["expanded"]
                  }`}
                >
                  <div className={styles["config-content"]}>
                    <div className={styles["form-group"]}>
                      <label>API 密钥</label>
                      <input
                        type="password"
                        value={currentFormData.apiKey}
                        placeholder="请输入API密钥"
                        onChange={(e) =>
                          handleFormChange(
                            provider.id,
                            "apiKey",
                            e.target.value,
                          )
                        }
                        className={styles["form-input"]}
                      />
                    </div>
                    <div className={styles["form-group"]}>
                      <label>Base URL</label>
                      <input
                        type="text"
                        value={currentFormData.baseUrl}
                        placeholder="请输入自定义Base URL"
                        onChange={(e) =>
                          handleFormChange(
                            provider.id,
                            "baseUrl",
                            e.target.value,
                          )
                        }
                        className={styles["form-input"]}
                      />
                      <small className={styles["form-hint"]}>
                        留空使用默认URL:{" "}
                        {
                          BUILTIN_PROVIDERS.find((p) => p.id === provider.id)
                            ?.baseUrl
                        }
                      </small>
                    </div>

                    {/* 启用的模型配置 */}
                    <div className={styles["form-group"]}>
                      <label>启用的模型</label>
                      <div className={styles["models-section"]}>
                        <div className={styles["models-display"]}>
                          {provider.models && provider.models.length > 0 ? (
                            <div className={styles["model-tags"]}>
                              {provider.models.map((model, index) => (
                                <span
                                  key={index}
                                  className={styles["model-tag"]}
                                >
                                  {model}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className={styles["no-models"]}>
                              当前服务商中已启用的模型列表
                            </span>
                          )}
                        </div>
                        <button
                          className={styles["manage-models-button"]}
                          onClick={() => handleManageModels(provider)}
                        >
                          管理
                        </button>
                      </div>
                    </div>

                    <div className={styles["form-actions"]}>
                      <button
                        className={styles["save-button"]}
                        onClick={() => handleSave(provider.id)}
                      >
                        保存配置
                      </button>
                      <span className={styles["save-note"]}>
                        在对 baseurl、api
                        key、可用模型修改后，请点击保存以确保数据同步保存
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* 模型管理器 */}
      {showModelManager && currentProvider && (
        <ModelManager
          provider={currentProvider}
          onClose={() => {
            setShowModelManager(false);
            setCurrentProvider(null);
          }}
        />
      )}
    </div>
  );
}
