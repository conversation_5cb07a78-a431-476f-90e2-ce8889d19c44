import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

export type PaymentStatus =
  | "PENDING"
  | "PAID"
  | "FAILED"
  | "EXPIRED"
  | "CANCELLED";

export interface PaymentOrder {
  id: string;
  outTradeNo: string;
  amount: number;
  status: PaymentStatus;
  codeUrl?: string;
  qrcodeUrl?: string;
  expireTime?: string;
  successTime?: string;
  createdAt: string;
}

interface PaymentState {
  orders: PaymentOrder[];
  currentOrder: PaymentOrder | null;
  isLoading: boolean;
  error: string | null;
}

interface PaymentActions {
  setCurrentOrder: (order: PaymentOrder | null) => void;
  addOrder: (order: PaymentOrder) => void;
  updateOrder: (orderId: string, updates: Partial<PaymentOrder>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  clearCurrentOrder: () => void;
  getOrderById: (orderId: string) => PaymentOrder | undefined;
  getPendingOrders: () => PaymentOrder[];
  clearExpiredOrders: () => void;
}

const DEFAULT_STATE: PaymentState = {
  orders: [],
  currentOrder: null,
  isLoading: false,
  error: null,
};

export const usePaymentStore = create<PaymentState & PaymentActions>()(
  persist(
    (set, get) => ({
      ...DEFAULT_STATE,

      setCurrentOrder: (order: PaymentOrder | null) => {
        set({ currentOrder: order });
      },

      addOrder: (order: PaymentOrder) => {
        set((state) => ({
          orders: [order, ...state.orders],
          currentOrder: order,
        }));
      },

      updateOrder: (orderId: string, updates: Partial<PaymentOrder>) => {
        set((state) => ({
          orders: state.orders.map((order) =>
            order.id === orderId ? { ...order, ...updates } : order,
          ),
          currentOrder:
            state.currentOrder?.id === orderId
              ? { ...state.currentOrder, ...updates }
              : state.currentOrder,
        }));
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      clearCurrentOrder: () => {
        set({ currentOrder: null });
      },

      getOrderById: (orderId: string) => {
        const { orders } = get();
        return orders.find((order) => order.id === orderId);
      },

      getPendingOrders: () => {
        const { orders } = get();
        return orders.filter((order) => order.status === "PENDING");
      },

      clearExpiredOrders: () => {
        const now = new Date();
        set((state) => ({
          orders: state.orders.filter((order) => {
            if (order.status !== "PENDING" || !order.expireTime) {
              return true;
            }
            return new Date(order.expireTime) > now;
          }),
        }));
      },
    }),
    {
      name: "payment-store",
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        orders: state.orders,
        // 不持久化 currentOrder，每次重新加载时都清空
      }),
      onRehydrateStorage: () => (state) => {
        console.log("[PaymentStore] Hydration completed");
        // 清理过期订单
        if (state) {
          state.clearExpiredOrders();
        }
      },
    },
  ),
);

// 支付相关的工具函数
export const PaymentUtils = {
  /**
   * 格式化支付状态显示文本
   */
  formatStatus: (status: PaymentStatus): string => {
    const statusMap = {
      PENDING: "待支付",
      PAID: "已支付",
      FAILED: "支付失败",
      EXPIRED: "已过期",
      CANCELLED: "已取消",
    };
    return statusMap[status] || "未知状态";
  },

  /**
   * 获取支付状态对应的颜色
   */
  getStatusColor: (status: PaymentStatus): string => {
    const colorMap = {
      PENDING: "#ff9800", // 橙色
      PAID: "#4caf50", // 绿色
      FAILED: "#f44336", // 红色
      EXPIRED: "#9e9e9e", // 灰色
      CANCELLED: "#9e9e9e", // 灰色
    };
    return colorMap[status] || "#9e9e9e";
  },

  /**
   * 检查订单是否已过期
   */
  isOrderExpired: (order: PaymentOrder): boolean => {
    if (!order.expireTime || order.status !== "PENDING") {
      return false;
    }
    return new Date() > new Date(order.expireTime);
  },

  /**
   * 计算订单剩余时间（秒）
   */
  getRemainingTime: (order: PaymentOrder): number => {
    if (!order.expireTime || order.status !== "PENDING") {
      return 0;
    }
    const remaining = new Date(order.expireTime).getTime() - Date.now();
    return Math.max(0, Math.floor(remaining / 1000));
  },

  /**
   * 格式化倒计时显示
   */
  formatCountdown: (seconds: number): string => {
    if (seconds <= 0) return "00:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  },

  /**
   * 格式化金额显示
   */
  formatAmount: (amount: number): string => {
    return `¥${amount.toFixed(2)}`;
  },

  /**
   * 生成订单摘要
   */
  getOrderSummary: (order: PaymentOrder): string => {
    return `充值 ${PaymentUtils.formatAmount(
      order.amount,
    )} - ${PaymentUtils.formatStatus(order.status)}`;
  },
};
