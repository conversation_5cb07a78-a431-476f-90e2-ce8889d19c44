import crypto from "crypto";

// 蓝兔支付配置
export interface LTZFConfig {
  mchId: string;
  apiKey: string;
  notifyUrl: string;
}

// 支付请求参数
export interface PaymentRequest {
  outTradeNo: string;
  totalFee: string;
  body: string;
  attach?: string;
  timeExpire?: string;
}

// 支付响应
export interface PaymentResponse {
  code: number;
  data?: {
    code_url: string;
    QRcode_url: string;
  };
  msg: string;
  request_id: string;
}

// 支付通知参数
export interface PaymentNotify {
  code: string;
  timestamp: string;
  mch_id: string;
  order_no: string;
  out_trade_no: string;
  pay_no: string;
  total_fee: string;
  sign: string;
  pay_channel?: string;
  trade_type?: string;
  success_time?: string;
  attach?: string;
  openid?: string;
}

/**
 * 生成签名 - 按照蓝兔支付官方文档要求
 */
export function generateSign(
  params: Record<string, string>,
  apiKey: string,
): string {
  // 1. 过滤空值
  const filteredParams: Record<string, string> = {};
  Object.keys(params).forEach((key) => {
    const value = params[key];
    if (value !== "" && value !== undefined && value !== null) {
      filteredParams[key] = value;
    }
  });

  // 2. 按参数名ASCII码从小到大排序
  const sortedKeys = Object.keys(filteredParams).sort();

  // 3. 构建签名字符串：参数名1=参数值1&参数名2=参数值2...&key=API密钥
  const signString =
    sortedKeys.map((key) => `${key}=${filteredParams[key]}`).join("&") +
    `&key=${apiKey}`;

  console.log("[Payment] Filtered params:", filteredParams);
  console.log("[Payment] Sorted keys:", sortedKeys);
  console.log("[Payment] Sign string:", signString);

  // 4. MD5加密并转大写
  const sign = crypto
    .createHash("md5")
    .update(signString, "utf8")
    .digest("hex")
    .toUpperCase();
  console.log("[Payment] Generated sign:", sign);

  return sign;
}

/**
 * 验证签名
 */
export function verifySign(
  params: Record<string, string>,
  apiKey: string,
  sign: string,
): boolean {
  const calculatedSign = generateSign(params, apiKey);
  return calculatedSign === sign;
}

/**
 * 测试签名算法 - 使用官方文档示例
 */
function testSignature() {
  // 根据官方文档示例，我们需要找到正确的API密钥来验证
  // 先用一个简单的测试来验证我们的算法
  const testParams = {
    mch_id: "1230000109",
    out_trade_no: "LTZF2022113023096",
    total_fee: "0.01",
    body: "Image形象店-深圳腾大-QQ公仔",
    timestamp: "1669533132",
    notify_url: "https://www.weixin.qq.com/wxpay/pay.php",
    attach: "自定义数据",
    time_expire: "5m",
    developer_appid: "1041589049015120",
  };

  // 我们不知道官方示例的真实API密钥，所以先测试我们的算法逻辑
  console.log("[Payment] Test signature generation process:");
  const testSign = generateSign(testParams, "test-key");
  console.log("[Payment] Test sign result:", testSign);
}

/**
 * 调用蓝兔支付API
 */
export async function callLTZFAPI(
  config: LTZFConfig,
  paymentData: PaymentRequest,
): Promise<PaymentResponse> {
  console.log("[Payment] Creating payment order with config:", {
    mchId: config.mchId,
    notifyUrl: config.notifyUrl,
    apiKeyLength: config.apiKey?.length || 0,
  });

  // 运行签名测试（仅在开发环境）
  if (process.env.NODE_ENV !== "production") {
    testSignature();

    // 测试当前请求的签名字符串格式
    console.log("[Payment] Current request signature test:");
    console.log("[Payment] Using same params as actual request...");
  }

  const timestamp = Math.floor(Date.now() / 1000).toString();
  console.log(
    "[Payment] Current timestamp:",
    timestamp,
    "Date:",
    new Date(parseInt(timestamp) * 1000).toISOString(),
  );

  const params = {
    mch_id: config.mchId,
    out_trade_no: paymentData.outTradeNo,
    total_fee: paymentData.totalFee,
    body: paymentData.body,
    timestamp,
    notify_url: config.notifyUrl,
    ...(paymentData.attach && { attach: paymentData.attach }),
    ...(paymentData.timeExpire && { time_expire: paymentData.timeExpire }),
  };

  console.log("[Payment] Request params before sign:", params);

  // 生成签名
  const sign = generateSign(params, config.apiKey);
  const requestData = { ...params, sign };

  // 构建表单数据 - 注意：签名计算和表单提交使用相同的参数值
  const formData = new URLSearchParams();
  Object.keys(requestData).forEach((key) => {
    const value = requestData[key as keyof typeof requestData];
    if (value !== undefined) {
      formData.append(key, value);
    }
  });

  console.log("[Payment] Form data entries:");
  for (const [key, value] of formData.entries()) {
    console.log(`  ${key}: ${value}`);
  }

  try {
    console.log("[Payment] Sending request to LTZF API:", formData.toString());

    const response = await fetch("https://api.ltzf.cn/api/wxpay/native", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: formData.toString(),
    });

    const result = await response.json();
    console.log("[Payment] LTZF API response:", result);

    return result as PaymentResponse;
  } catch (error) {
    console.error("[Payment] LTZF API call failed:", error);
    throw new Error("支付接口调用失败");
  }
}

/**
 * 生成订单号
 */
export function generateOrderNo(): string {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, "0");
  return `QADCHAT${timestamp}${random}`;
}

/**
 * 获取支付配置
 */
export function getPaymentConfig(): LTZFConfig {
  const mchId = process.env.LTZF_MCH_ID;
  const apiKey = process.env.LTZF_API_KEY;
  let notifyUrl = process.env.LTZF_NOTIFY_URL;

  if (!mchId || !apiKey || !notifyUrl) {
    throw new Error("支付配置不完整，请检查环境变量");
  }

  // 开发环境使用ngrok或其他内网穿透工具
  // 如果是localhost，提醒用户配置外网可访问的URL
  if (notifyUrl.includes("localhost") || notifyUrl.includes("127.0.0.1")) {
    console.warn(
      "[Payment] 警告: 回调URL使用localhost，支付回调可能无法正常工作",
    );
    console.warn("[Payment] 建议使用ngrok等工具提供外网可访问的URL");
  }

  return {
    mchId,
    apiKey,
    notifyUrl,
  };
}
