import jwt from "jsonwebtoken";
import { NextRequest } from "next/server";

const JWT_SECRET =
  process.env.JWT_SECRET || "default-secret-change-in-production";
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";

export interface JWTPayload {
  userId: string;
  username: string;
  email: string;
  role: "USER" | "ADMIN";
  iat?: number;
  exp?: number;
}

/**
 * 生成JWT token
 */
export function generateToken(
  payload: Omit<JWTPayload, "iat" | "exp">,
): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
}

/**
 * 验证JWT token
 */
export function verifyToken(token: string): JWTPayload | null {
  try {
    console.log(`[JWT Debug] Verifying token: ${token.substring(0, 20)}...`);
    const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload;
    console.log(
      `[JWT Debug] Token verified successfully for user: ${decoded.userId}, role: ${decoded.role}`,
    );
    return decoded;
  } catch (error) {
    console.error(
      `[JWT Error] Verification failed for token: ${token.substring(0, 20)}...`,
      error,
    );
    throw error; // 抛出错误而不是返回null，这样我们能看到具体的错误
  }
}

/**
 * 从请求中提取JWT token
 */
export function extractTokenFromRequest(req: NextRequest): string | null {
  // 从 Authorization header 中提取
  const authHeader = req.headers.get("Authorization");
  console.log(
    `[JWT Debug] Authorization header: ${
      authHeader ? authHeader.substring(0, 20) + "..." : "null"
    }`,
  );

  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.substring(7);

    // 检查是否是API Key而不是JWT token
    if (
      token.startsWith("sk-") ||
      token.startsWith("gsk_") ||
      token.startsWith("claude-") ||
      token.startsWith("AIza")
    ) {
      console.log(
        `[JWT Debug] Detected API Key instead of JWT token: ${token.substring(
          0,
          10,
        )}...`,
      );
      // 这是API Key，不是JWT token，跳过
    } else {
      console.log(
        `[JWT Debug] Token extracted from header: ${token.substring(0, 20)}...`,
      );
      return token;
    }
  }

  // 从 cookie 中提取
  const tokenFromCookie = req.cookies.get("auth-token")?.value;
  console.log(
    `[JWT Debug] Token from cookie: ${
      tokenFromCookie ? tokenFromCookie.substring(0, 20) + "..." : "null"
    }`,
  );

  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  console.log("[JWT Debug] No token found in request");
  return null;
}

/**
 * 验证用户token并返回用户信息
 */
export function verifyUserToken(req: NextRequest): JWTPayload | null {
  const token = extractTokenFromRequest(req);
  if (!token) {
    console.log("[JWT Debug] No token found in request");
    return null;
  }

  try {
    return verifyToken(token);
  } catch (error) {
    console.error("[JWT Error] Token verification failed:", error);
    return null;
  }
}

/**
 * 验证用户token并检查用户状态（包括是否被禁用）
 */
export async function verifyUserTokenWithStatus(req: NextRequest): Promise<{
  payload: JWTPayload | null;
  user: any | null;
  error?: string;
}> {
  const payload = verifyUserToken(req);
  if (!payload) {
    return { payload: null, user: null, error: "未授权访问" };
  }

  try {
    // 从数据库检查用户状态
    const { prisma } = await import("@/app/lib/prisma");
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        enabled: true,
        pointsBalance: true,
      },
    });

    if (!user) {
      return { payload: null, user: null, error: "用户不存在" };
    }

    if (!user.enabled) {
      return { payload: null, user: null, error: "账户已被禁用" };
    }

    return { payload, user, error: undefined };
  } catch (error) {
    console.error("[JWT Error] Database check failed:", error);
    return { payload: null, user: null, error: "用户状态检查失败" };
  }
}

/**
 * 验证管理员权限
 */
export function verifyAdminToken(req: NextRequest): JWTPayload | null {
  const payload = verifyUserToken(req);
  if (!payload || payload.role !== "ADMIN") {
    return null;
  }

  return payload;
}

/**
 * 刷新token
 */
export function refreshToken(oldToken: string): string | null {
  const payload = verifyToken(oldToken);
  if (!payload) {
    return null;
  }

  // 移除旧的时间戳
  const { iat, exp, ...userPayload } = payload;

  return generateToken(userPayload);
}
