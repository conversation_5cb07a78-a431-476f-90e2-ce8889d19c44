import { useSystemConfigStore } from "../store";

/**
 * 获取后端模式下的统一API路径
 * 在后端模式下，所有聊天请求都应该使用 /api/chat 统一路由
 */
export function getBackendApiPath(
  originalPath: string,
  providerType: string,
): string | null {
  const systemConfigStore = useSystemConfigStore.getState();
  const isBackendMode = systemConfigStore.isBackendMode();

  // 检查是否为聊天相关的路径
  const chatPaths = [
    "v1/chat/completions",
    "v1/messages", // Claude
    "api/v3/chat/completions", // ByteDance
    "chat/completions", // DeepSeek
  ];

  const isChatPath = chatPaths.some((path) => originalPath.includes(path));

  if (isBackendMode && isChatPath) {
    console.log(
      `[BackendAPI] Using unified route for ${providerType}: /api/chat`,
    );
    return "/api/chat";
  }

  return null; // 返回null表示不使用后端统一路由
}

/**
 * 为后端模式的请求添加provider信息
 */
export function addProviderToPayload(payload: any, providerType: string): any {
  const systemConfigStore = useSystemConfigStore.getState();
  const isBackendMode = systemConfigStore.isBackendMode();

  if (isBackendMode) {
    return {
      ...payload,
      provider: providerType,
    };
  }

  return payload;
}

/**
 * 获取服务商类型映射
 */
export function getProviderTypeFromModelProvider(
  modelProvider: string,
): string {
  const mapping: Record<string, string> = {
    GPT: "openai",
    GeminiPro: "google",
    Claude: "anthropic",
    Doubao: "bytedance",
    Qwen: "alibaba",
    Moonshot: "moonshot",
    XAI: "xai",
    DeepSeek: "deepseek",
    SiliconFlow: "siliconflow",
  };

  return mapping[modelProvider] || "openai";
}
