import { useMemo, useRef } from "react";
import { useAccessStore, useAppConfig } from "../store";
import { useSystemConfigStore } from "../store/system-config";
import { collectModelsWithDefaultModel } from "./model";
import { LLMModel } from "../client/api";
import { DEFAULT_MODELS } from "../constant";

// 全局模型缓存，避免重复计算
let globalModelsCache: {
  models: LLMModel[];
  configHash: string;
} | null = null;

export function useAllModels() {
  const systemConfigStore = useSystemConfigStore();
  const renderCountRef = useRef(0);
  renderCountRef.current++;

  // 获取调用栈信息来识别是哪个组件调用的
  const getCallerInfo = () => {
    const stack = new Error().stack;
    const lines = stack?.split("\n") || [];
    // 查找调用useAllModels的组件
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (
        line.includes("Chat") ||
        line.includes("ModelConfig") ||
        line.includes("Mask")
      ) {
        return line.trim();
      }
    }
    return "Unknown caller";
  };

  const callerInfo = getCallerInfo();
  console.log(
    `[useAllModels] Hook called (render #${renderCountRef.current}) by: ${callerInfo}`,
  );

  const models = useMemo(() => {
    console.log(`[useAllModels] Computing models for caller: ${callerInfo}`);

    // 强制要求后端模式
    const isBackendMode = systemConfigStore.isBackendMode();
    console.log("[useAllModels] Backend mode:", isBackendMode);

    if (!isBackendMode) {
      console.warn("[useAllModels] ⚠️ 非后端模式，返回默认模型列表");
      // 在非后端模式下，返回默认模型列表
      return DEFAULT_MODELS.filter((model) => model.available !== false);
    }

    // 创建配置哈希来检查是否需要重新计算
    const config = systemConfigStore.config;
    const configHash = JSON.stringify({
      providers: config?.providers,
      lastUpdated: systemConfigStore.lastUpdated,
    });

    // 检查全局缓存
    if (globalModelsCache && globalModelsCache.configHash === configHash) {
      console.log("[useAllModels] Using cached models");
      return globalModelsCache.models;
    }

    console.log("[useAllModels] Computing new models");

    // 后端模式：从系统配置获取启用的服务商和模型
    const availableProviders = systemConfigStore.getAvailableProviders();
    console.log("[useAllModels] Available providers:", availableProviders);

    if (availableProviders.length === 0) {
      console.warn("[useAllModels] ⚠️ 没有可用的服务商");
      return [];
    }

    const backendModels: LLMModel[] = [];

    availableProviders.forEach((provider) => {
      console.log(
        `[useAllModels] Processing provider: ${provider.name} (${provider.type})`,
      );
      console.log(
        `[useAllModels] Provider enabled: ${provider.enabled}, models: ${
          provider.models?.length || 0
        }`,
      );

      if (provider.enabled && provider.models && provider.models.length > 0) {
        // 获取该服务商类型的基础模型
        const baseModelsForType = getBaseModelsForProviderType(provider.type);
        console.log(
          `[useAllModels] Base models for ${provider.type}:`,
          baseModelsForType.length,
        );

        provider.models.forEach((modelName) => {
          console.log(`[useAllModels] Processing model: ${modelName}`);

          // 查找对应的基础模型
          const baseModel = baseModelsForType.find((m) => m.name === modelName);
          if (baseModel) {
            console.log(`[useAllModels] Found base model for ${modelName}`);
            backendModels.push({
              ...baseModel,
              available: true,
              displayName: baseModel.displayName || baseModel.name,
              isDefault: baseModel.isDefault || false,
              provider: {
                id: provider.type, // 使用服务商类型作为ID
                providerName: baseModel.provider.providerName,
                providerType: provider.type,
                sorted: baseModel.provider.sorted,
              },
            });
          } else {
            // 如果找不到基础模型，创建一个新的模型条目
            console.log(
              `[useAllModels] Creating new model entry for ${modelName}`,
            );
            backendModels.push({
              name: modelName,
              displayName: modelName,
              available: true,
              sorted: 1000,
              isDefault: false,
              provider: {
                id: provider.type,
                providerName: provider.name,
                providerType: provider.type,
                sorted: 1000,
              },
            });
          }
        });
      }
    });

    if (backendModels.length === 0) {
      console.warn("[useAllModels] ⚠️ 没有可用的模型");
      return [];
    }

    console.log("[useAllModels] Final backend models:", backendModels);

    // 更新全局缓存
    globalModelsCache = {
      models: backendModels,
      configHash,
    };

    return backendModels;
  }, [systemConfigStore.config, systemConfigStore.lastUpdated]);

  return models;
}

// 根据服务商类型获取基础模型列表
function getBaseModelsForProviderType(type: string): LLMModel[] {
  const { ServiceProvider } = require("../constant");

  switch (type) {
    case "openai":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.OpenAI,
      );
    case "google":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.Google,
      );
    case "anthropic":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.Anthropic,
      );
    case "bytedance":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.ByteDance,
      );
    case "alibaba":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.Alibaba,
      );
    case "moonshot":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.Moonshot,
      );
    case "xai":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.XAI,
      );
    case "deepseek":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.DeepSeek,
      );
    case "siliconflow":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) =>
          m.provider.providerName === ServiceProvider.SiliconFlow,
      );
    case "azure":
      return DEFAULT_MODELS.filter(
        (m: LLMModel) => m.provider.providerName === ServiceProvider.Azure,
      );
    default:
      return [];
  }
}
