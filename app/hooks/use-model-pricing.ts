import { useState, useEffect } from "react";
import { useUserStore } from "../store/user";
import React from "react";

interface ModelPricing {
  id: string;
  model: string;
  billingType: "TOKEN" | "COUNT";
  inputTokenPrice: number;
  outputTokenPrice: number;
  countPrice?: number;
  enabled: boolean;
  description?: string;
}

export function useModelPricing() {
  const [modelPricings, setModelPricings] = useState<ModelPricing[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const userStore = useUserStore();

  useEffect(() => {
    const fetchModelPricings = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch("/api/model-pricing", {
          headers: {
            Authorization: `Bearer ${userStore.token}`,
          },
        });

        if (!response.ok) {
          throw new Error("获取模型定价失败");
        }

        const result = await response.json();
        if (result.success) {
          setModelPricings(result.data || []);
        } else {
          throw new Error(result.error || "获取模型定价失败");
        }
      } catch (err) {
        console.error("Failed to fetch model pricings:", err);
        setError(err instanceof Error ? err.message : "获取模型定价失败");
      } finally {
        setLoading(false);
      }
    };

    // 只有在有token的情况下才获取定价信息
    if (userStore.token) {
      fetchModelPricings();
    } else {
      setLoading(false);
    }
  }, [userStore.token]);

  // 根据模型名称获取定价信息
  const getPricingByModel = (modelName: string): ModelPricing | null => {
    return modelPricings.find((pricing) => pricing.model === modelName) || null;
  };

  // 格式化价格显示
  const formatPricing = (pricing: ModelPricing | null): string => {
    if (!pricing) {
      return "价格未设置";
    }

    if (pricing.billingType === "COUNT") {
      return `☁️${(pricing.countPrice || 0).toFixed(2)} 点数/次`;
    } else {
      // Token计费：显示输入/输出详细信息，格式类似 "11.00/22.00 点数/1K tokens"
      const inputPrice = pricing.inputTokenPrice.toFixed(2);
      const outputPrice = pricing.outputTokenPrice.toFixed(2);
      return `${inputPrice}/${outputPrice} 点数/1K tokens`;
    }
  };

  // 格式化价格显示为JSX元素（更详细的显示）
  const formatPricingDetailed = (pricing: ModelPricing | null): React.ReactNode => {
    if (!pricing) {
      return <span style={{ color: "#999", fontSize: "12px" }}>价格未设置</span>;
    }

    const baseStyle = {
      color: "#666",
      fontSize: "12px",
      fontFamily: "monospace",
    };

    if (pricing.billingType === "COUNT") {
      return (
        <span style={baseStyle}>
          ☁️{(pricing.countPrice || 0).toFixed(2)} 点数/次
        </span>
      );
    } else {
      // Token计费：显示输入/输出详细信息，格式类似图片中的样式
      const inputPrice = pricing.inputTokenPrice.toFixed(2);
      const outputPrice = pricing.outputTokenPrice.toFixed(2);
      return (
        <span style={baseStyle}>
          {inputPrice}/{outputPrice} 点数/1K tokens
        </span>
      );
    }
  };

  return {
    modelPricings,
    loading,
    error,
    getPricingByModel,
    formatPricing,
    formatPricingDetailed,
  };
}
