"use client";

import React, { useState, useEffect } from "react";
import styles from "./logs-management.module.scss";

interface ApiLog {
  id: string;
  userId: string | null;
  providerId: string;
  model: string;
  endpoint: string;
  method: string;
  tokensUsed: number | null;
  promptTokens: number | null;
  completionTokens: number | null;
  cost: number | null;
  status: "success" | "error" | "timeout";
  errorMsg: string | null;
  duration: number | null;
  createdAt: string;
  user?: {
    id: string;
    username: string;
    email: string;
  };
  provider?: {
    id: string;
    name: string;
    type: string;
  };
}

interface LogsResponse {
  logs: ApiLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  stats: {
    totalCalls: number;
    successCalls: number;
    errorCalls: number;
    successRate: string;
    totalTokens: number;
    totalCost: number;
  };
}

export function LogsManagement() {
  const [logs, setLogs] = useState<ApiLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });
  const [stats, setStats] = useState({
    totalCalls: 0,
    successCalls: 0,
    errorCalls: 0,
    successRate: "0",
    totalTokens: 0,
    totalCost: 0,
  });
  const [filters, setFilters] = useState({
    userId: "",
    providerId: "",
    status: "",
    startDate: "",
    endDate: "",
    model: "",
  });

  // 获取日志列表
  const fetchLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== ""),
        ),
      });

      const response = await fetch(`/api/admin/logs?${params}`);
      if (!response.ok) {
        throw new Error("获取日志失败");
      }

      const data: LogsResponse = await response.json();
      setLogs(data.logs);
      setPagination(data.pagination);
      setStats(data.stats);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取日志失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [pagination.page, filters]);

  // 格式化数字
  const formatNumber = (num: number | null) => {
    return num ? num.toLocaleString() : "-";
  };

  // 格式化成本
  const formatCost = (cost: number | null) => {
    return cost ? `☁️${cost.toFixed(2)} 点数` : "-";
  };

  // 格式化持续时间
  const formatDuration = (duration: number | null) => {
    return duration ? `${duration}ms` : "-";
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  // 获取状态样式
  const getStatusClass = (status: string) => {
    switch (status) {
      case "success":
        return styles.success;
      case "error":
        return styles.error;
      case "timeout":
        return styles.timeout;
      default:
        return "";
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case "success":
        return "成功";
      case "error":
        return "错误";
      case "timeout":
        return "超时";
      default:
        return status;
    }
  };

  if (loading) {
    return <div className={styles.loading}>加载中...</div>;
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>API调用日志</h2>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      {/* 统计信息 */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>
            {formatNumber(stats.totalCalls)}
          </div>
          <div className={styles.statLabel}>总调用次数</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>
            {formatNumber(stats.successCalls)}
          </div>
          <div className={styles.statLabel}>成功调用</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>
            {formatNumber(stats.errorCalls)}
          </div>
          <div className={styles.statLabel}>失败调用</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{stats.successRate}%</div>
          <div className={styles.statLabel}>成功率</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>
            {formatNumber(stats.totalTokens)}
          </div>
          <div className={styles.statLabel}>总Token数</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{formatCost(stats.totalCost)}</div>
          <div className={styles.statLabel}>总消耗</div>
        </div>
      </div>

      {/* 筛选器 */}
      <div className={styles.filters}>
        <input
          type="text"
          placeholder="用户ID..."
          value={filters.userId}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, userId: e.target.value }))
          }
          className={styles.filterInput}
        />
        <input
          type="text"
          placeholder="服务商ID..."
          value={filters.providerId}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, providerId: e.target.value }))
          }
          className={styles.filterInput}
        />
        <select
          value={filters.status}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, status: e.target.value }))
          }
          className={styles.filterSelect}
        >
          <option value="">所有状态</option>
          <option value="success">成功</option>
          <option value="error">错误</option>
          <option value="timeout">超时</option>
        </select>
        <input
          type="text"
          placeholder="模型名称..."
          value={filters.model}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, model: e.target.value }))
          }
          className={styles.filterInput}
        />
        <input
          type="datetime-local"
          value={filters.startDate}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, startDate: e.target.value }))
          }
          className={styles.filterInput}
        />
        <input
          type="datetime-local"
          value={filters.endDate}
          onChange={(e) =>
            setFilters((prev) => ({ ...prev, endDate: e.target.value }))
          }
          className={styles.filterInput}
        />
        <button
          onClick={() => {
            setFilters({
              userId: "",
              providerId: "",
              status: "",
              startDate: "",
              endDate: "",
              model: "",
            });
            setPagination((prev) => ({ ...prev, page: 1 }));
          }}
          className={styles.clearButton}
        >
          清除筛选
        </button>
      </div>

      {/* 日志表格 */}
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>时间</th>
              <th>用户</th>
              <th>服务商</th>
              <th>模型</th>
              <th>状态</th>
              <th>Token</th>
              <th>成本</th>
              <th>耗时</th>
              <th>错误信息</th>
            </tr>
          </thead>
          <tbody>
            {logs.map((log) => (
              <tr key={log.id}>
                <td>{formatDate(log.createdAt)}</td>
                <td>
                  {log.user ? (
                    <div>
                      <div>{log.user.username}</div>
                      <div className={styles.subText}>{log.user.email}</div>
                    </div>
                  ) : (
                    <span className={styles.anonymous}>匿名用户</span>
                  )}
                </td>
                <td>
                  {log.provider ? (
                    <div>
                      <div>{log.provider.name}</div>
                      <div className={styles.subText}>{log.provider.type}</div>
                    </div>
                  ) : (
                    log.providerId
                  )}
                </td>
                <td>{log.model}</td>
                <td>
                  <span
                    className={`${styles.status} ${getStatusClass(log.status)}`}
                  >
                    {getStatusText(log.status)}
                  </span>
                </td>
                <td>
                  {log.tokensUsed ? (
                    <div>
                      <div>{formatNumber(log.tokensUsed)}</div>
                      {log.promptTokens && log.completionTokens && (
                        <div className={styles.subText}>
                          {formatNumber(log.promptTokens)} /{" "}
                          {formatNumber(log.completionTokens)}
                        </div>
                      )}
                    </div>
                  ) : (
                    "-"
                  )}
                </td>
                <td>{formatCost(log.cost)}</td>
                <td>{formatDuration(log.duration)}</td>
                <td>
                  {log.errorMsg && (
                    <div className={styles.errorMsg} title={log.errorMsg}>
                      {log.errorMsg.length > 50
                        ? `${log.errorMsg.substring(0, 50)}...`
                        : log.errorMsg}
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      <div className={styles.pagination}>
        <button
          onClick={() =>
            setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
          }
          disabled={pagination.page <= 1}
          className={styles.pageButton}
        >
          上一页
        </button>
        <span className={styles.pageInfo}>
          第 {pagination.page} 页，共 {pagination.pages} 页
        </span>
        <button
          onClick={() =>
            setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
          }
          disabled={pagination.page >= pagination.pages}
          className={styles.pageButton}
        >
          下一页
        </button>
      </div>
    </div>
  );
}
