.admin-dashboard {
  padding: 0;
  height: auto;
  overflow: visible;
  background: transparent;
  min-height: auto;
}

.dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: var(--black);
  opacity: 0.7;
  background: transparent;
  border-radius: 0;
  padding: 40px;
  margin: 0;
  box-shadow: none;
}

.dashboard-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: var(--second);
  border-radius: 0;
  box-shadow: none;
  border: none;
  border-bottom: var(--border-in-light);

  h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 8px 0;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
  }

  p {
    font-size: 14px;
    color: var(--black);
    opacity: 0.7;
    margin: 0;
    font-weight: 400;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding: 0 24px;
}

.stat-card {
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--white);
  border-radius: 8px;
  border: var(--border-in-light);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  &::before {
    display: none;
  }

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.stat-icon {
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  color: white;
  border-radius: 8px;
  box-shadow: none;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 4px;
  line-height: 1.2;
}

.stat-label {
  font-size: 12px;
  color: var(--black);
  opacity: 0.7;
  font-weight: 400;
  text-transform: none;
  letter-spacing: 0;
}

.recent-activity {
  background: var(--white);
  border-radius: 8px;
  border: var(--border-in-light);
  box-shadow: none;
  overflow: hidden;
  margin: 0 24px 24px 24px;

  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: var(--border-in-light);
    background: var(--second);

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--black);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '⚡';
        font-size: 16px;
      }
    }

    .refresh-button {
      background: var(--primary);
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: none;

      &:hover {
        background: var(--primary-dark);
      }
    }
  }
}

.activity-list {
  padding: 0 20px 20px 20px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: var(--border-in-light);
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: var(--hover-color);
    border-radius: 6px;
    padding: 12px 8px;
    margin: 0 -8px;
  }
}

.activity-icon {
  font-size: 14px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  color: white;
  border-radius: 6px;
  box-shadow: none;
}

.activity-content {
  flex: 1;
}

.activity-message {
  font-size: 14px;
  color: var(--black);
  margin-bottom: 4px;
  font-weight: 400;
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
  font-family: inherit;
}

.activity-status {
  font-size: 10px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: none;
  letter-spacing: 0;

  &.success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.error {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.warning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    padding: 20px;
    margin-bottom: 24px;

    h1 {
      font-size: 24px;
    }

    p {
      font-size: 14px;
    }
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .stat-card {
    padding: 20px;
    gap: 16px;
  }

  .stat-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-label {
    font-size: 13px;
  }

  .charts-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .chart-card {
    padding: 20px;
  }

  .chart-header h3 {
    font-size: 18px;
  }

  .recent-activity .activity-header {
    padding: 20px;
  }

  .activity-list {
    padding: 0 20px 20px 20px;
  }

  .trend-item {
    gap: 12px;
  }

  .trend-date {
    min-width: 45px;
    font-size: 12px;
  }

  .trend-value {
    min-width: 35px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .admin-dashboard {
    padding: 12px;
  }

  .dashboard-header {
    padding: 16px;

    h1 {
      font-size: 20px;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }

  .charts-section {
    gap: 16px;
  }

  .chart-card {
    padding: 16px;
  }
}

// 新增的图表区域样式
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  padding: 24px;
  background: var(--white);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--card-shadow-hover);
  }
}

.chart-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid var(--gray-100);

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '';
      width: 4px;
      height: 20px;
      background: linear-gradient(135deg, var(--primary), var(--primary-hover));
      border-radius: 2px;
    }
  }
}

.models-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.model-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--gray-50);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:hover {
    background: var(--gray-100);
    border-color: var(--primary);
    transform: translateX(4px);
  }
}

.model-rank {
  font-size: 16px;
  font-weight: 700;
  color: var(--white);
  background: linear-gradient(135deg, var(--primary), var(--primary-hover));
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  box-shadow: 0 2px 8px rgba(29, 147, 171, 0.3);
}

.model-info {
  flex: 1;
}

.model-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.model-stats {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.trend-chart {
  height: 240px;
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.trend-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
  transition: all 0.2s ease;

  &:hover {
    background: var(--gray-alpha);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 0 -12px;
  }
}

.trend-date {
  font-size: 13px;
  color: var(--text-secondary);
  min-width: 50px;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', monospace;
}

.trend-bar {
  flex: 1;
  height: 12px;
  background: var(--gray-200);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.trend-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--primary-hover));
  border-radius: 6px;
  transition: width 0.5s ease;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.trend-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 40px;
  text-align: right;
  font-family: 'Monaco', 'Menlo', monospace;
}

.billing-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.billing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--gray-50);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:hover {
    background: var(--gray-100);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--card-shadow);
  }
}

.billing-label {
  font-size: 15px;
  color: var(--text-secondary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '💰';
    font-size: 16px;
  }
}

.billing-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  font-family: 'Monaco', 'Menlo', monospace;
  background: linear-gradient(135deg, var(--success-color), #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.no-data {
  text-align: center;
  color: var(--text-muted);
  font-size: 16px;
  padding: 60px 20px;
  background: var(--gray-50);
  border-radius: 12px;
  border: 2px dashed var(--border-color);

  &::before {
    content: '📊';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
}
