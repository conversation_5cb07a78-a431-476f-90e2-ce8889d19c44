.container {
  padding: 0;
  background: transparent;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  margin-bottom: 20px;
}

.title-section {
  h2 {
    margin: 0;
    color: var(--black);
    font-size: 20px;
    font-weight: 600;
  }
}

// 移除自定义按钮样式，使用NextChat原生IconButton

.loading {
  text-align: center;
  padding: 60px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);

  &::before {
    content: '⏳';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.7;
  }
}

.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 12px;

  &::before {
    content: '⚠️';
    font-size: 20px;
  }
}

// 移除filters样式，现在使用List布局

.searchInput {
  width: 300px;
  padding: 8px 12px;
  border: 1px solid var(--border-in-light);
  border-radius: 6px;
  font-size: 14px;
  background: var(--white);
  color: var(--text-primary);

  &::placeholder {
    color: var(--text-muted);
  }

  &:focus {
    outline: none;
    border-color: var(--primary);
  }
}

.filterSelect {
  width: 150px;
  padding: 8px 12px;
  border: 1px solid var(--border-in-light);
  border-radius: 6px;
  font-size: 14px;
  background: var(--white);
  color: var(--text-primary);
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: var(--primary);
  }
}

// 移除表格样式，现在使用List布局

.role {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &.admin {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);

    &::before {
      content: '👑';
      font-size: 12px;
    }
  }

  &.user {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.2);

    &::before {
      content: '👤';
      font-size: 12px;
    }
  }
}

.status {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &.enabled {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(34, 197, 94, 0.2);

    &::before {
      content: '✅';
      font-size: 12px;
    }
  }

  &.disabled {
    background: #fee2e2;
    color: #dc2626;
  }
}

.actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

// 移除自定义按钮样式，使用NextChat的IconButton

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.pageButton {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--primary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.pageInfo {
  color: var(--gray-600);
  font-size: 14px;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filters {
    flex-direction: column;
  }

  .searchInput {
    min-width: auto;
  }

  .table {
    font-size: 12px;

    th,
    td {
      padding: 8px 4px;
    }
  }

  .actions {
    flex-direction: column;
    gap: 4px;
  }

  .viewButton,
  .enableButton,
  .disableButton,
  .resetButton,
  .deleteButton {
    width: 100%;
    text-align: center;
  }
}

// 新增样式支持List/ListItem布局
.content {
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-title {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  font-weight: 600;
  color: var(--text-primary);
}

.email {
  color: var(--text-secondary);
  font-size: 14px;
}

.role {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.admin {
    background: rgba(255, 193, 7, 0.1);
    color: #ff6b35;
  }

  &.user {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
  }
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.enabled {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
  }

  &.disabled {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }
}

.user-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;

  span {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
}

// 移除stat-group相关样式，现在直接在user-stats中处理

.actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

// 输入框样式
.text-input,
.select-input {
  width: 200px;
  padding: 8px 12px;
  border: 1px solid var(--border-in-light);
  border-radius: 6px;
  background: var(--white);
  color: var(--text-primary);
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: var(--primary);
  }

  &:disabled {
    background: var(--second);
    color: var(--text-muted);
    cursor: not-allowed;
  }
}

// 暗色主题适配
:global(.dark) {
  .text-input,
  .select-input {
    background: var(--white);
    color: var(--text-primary);
    border-color: var(--border-in-light);

    &:focus {
      border-color: var(--primary);
    }

    &:disabled {
      background: var(--second);
      color: var(--text-muted);
    }
  }

  .pageButton {
    background: var(--white);
    color: var(--text-primary);
    border-color: var(--border-in-light);

    &:hover:not(:disabled) {
      background: var(--second);
      border-color: var(--primary);
    }
  }

  .pageInfo {
    color: var(--text-secondary);
  }
}
