"use client";

import React, { useState, useEffect } from "react";
import { List, ListItem, Modal, showToast, showConfirm } from "../ui-lib";
import { IconButton } from "../button";
import styles from "./users-management.module.scss";

interface User {
  id: string;
  username: string;
  email: string;
  role: "USER" | "ADMIN";
  enabled: boolean;
  totalTokensUsed: number;
  totalCost: number;
  monthlyTokensUsed: number;
  monthlyCost: number;
  lastResetAt: string;
  createdAt: string;
  updatedAt: string;
  // 点数余额系统
  pointsBalance?: number;
  _count: {
    apiLogs: number;
    sessions: number;
  };
}

interface UsersResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function UsersManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [search, setSearch] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [enabledFilter, setEnabledFilter] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search,
        role: roleFilter,
        enabled: enabledFilter,
      });

      const response = await fetch(`/api/admin/users?${params}`);
      if (!response.ok) {
        throw new Error("获取用户列表失败");
      }

      const data: UsersResponse = await response.json();
      setUsers(data.users);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取用户列表失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [pagination.page, search, roleFilter, enabledFilter]);

  // 切换用户状态
  const toggleUserStatus = async (userId: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ enabled: !enabled }),
      });

      if (!response.ok) {
        throw new Error("更新用户状态失败");
      }

      await fetchUsers();
    } catch (err) {
      setError(err instanceof Error ? err.message : "更新用户状态失败");
    }
  };

  // 重置用户月度统计
  const resetUserStats = async (userId: string) => {
    const confirmed = await showConfirm({
      title: "确认重置",
      content: "确定要重置该用户的月度统计吗？",
    });

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/admin/users/${userId}/reset-stats`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("重置统计失败");
      }

      showToast("重置统计成功");
      await fetchUsers();
    } catch (err) {
      setError(err instanceof Error ? err.message : "重置统计失败");
    }
  };

  // 删除用户
  const deleteUser = async (userId: string) => {
    const confirmed = await showConfirm({
      title: "确认删除",
      content: "确定要删除该用户吗？此操作不可恢复！",
    });

    if (!confirmed) return;

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("删除用户失败");
      }

      showToast("删除用户成功");
      await fetchUsers();
    } catch (err) {
      setError(err instanceof Error ? err.message : "删除用户失败");
    }
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  // 格式化点数
  const formatPoints = (points: number | undefined) => {
    if (points === undefined || points === null) {
      return "☁️0.00 点数";
    }
    return `☁️${points.toFixed(2)} 点数`;
  };

  // 格式化点数余额（用于用户列表显示）
  const formatPointsBalance = (points: number | undefined) => {
    if (points === undefined || points === null) {
      return "余额: ☁️0.00";
    }
    return `余额: ☁️${points.toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  if (loading) {
    return <div className={styles.loading}>加载中...</div>;
  }

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div className={styles["page-header"]}>
        <div className={styles["header-content"]}>
          <div className={styles["title-section"]}>
            <h2>👥 用户管理</h2>
          </div>
          <IconButton
            text="添加用户"
            onClick={() => setShowCreateModal(true)}
            bordered
            shadow
          />
        </div>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      {/* 筛选器 */}
      <List>
        <ListItem title="搜索用户">
          <input
            type="text"
            placeholder="搜索用户名或邮箱..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={styles.searchInput}
          />
        </ListItem>
        <ListItem title="角色筛选">
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="">所有角色</option>
            <option value="USER">普通用户</option>
            <option value="ADMIN">管理员</option>
          </select>
        </ListItem>
        <ListItem title="状态筛选">
          <select
            value={enabledFilter}
            onChange={(e) => setEnabledFilter(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="">所有状态</option>
            <option value="true">已启用</option>
            <option value="false">已禁用</option>
          </select>
        </ListItem>
      </List>

      {/* 用户列表 */}
      <div className={styles.content}>
        <List>
          {users.map((user) => (
            <ListItem
              key={user.id}
              title={user.username}
              subTitle={
                <div className={styles["user-info"]}>
                  <div className={styles["user-title"]}>
                    <span className={styles["email"]}>{user.email}</span>
                    <span
                      className={`${styles.role} ${
                        user.role === "ADMIN" ? styles.admin : styles.user
                      }`}
                    >
                      {user.role === "ADMIN" ? "👑 管理员" : "👤 普通用户"}
                    </span>
                    <span
                      className={`${styles.status} ${
                        user.enabled ? styles.enabled : styles.disabled
                      }`}
                    >
                      {user.enabled ? "✅ 已启用" : "❌ 已禁用"}
                    </span>
                  </div>
                  <div className={styles["user-stats"]}>
                    <span>总Token: {formatNumber(user.totalTokensUsed)}</span>
                    <span>总消耗: {formatPoints(user.totalCost)}</span>
                    <span>
                      月度Token: {formatNumber(user.monthlyTokensUsed)}
                    </span>
                    <span>月度消耗: {formatPoints(user.monthlyCost)}</span>
                    <span>{formatPointsBalance(user.pointsBalance)}</span>
                    <span>API调用: {formatNumber(user._count.apiLogs)}</span>
                    <span>创建时间: {formatDate(user.createdAt)}</span>
                  </div>
                </div>
              }
            >
              <div className={styles.actions}>
                <IconButton
                  text="详情"
                  onClick={() => setSelectedUser(user)}
                  bordered
                />
                <IconButton
                  text="编辑"
                  onClick={() => setEditingUser(user)}
                  bordered
                />
                <IconButton
                  text={user.enabled ? "禁用" : "启用"}
                  onClick={() => toggleUserStatus(user.id, user.enabled)}
                  bordered
                />
                <IconButton
                  text="重置"
                  onClick={() => resetUserStats(user.id)}
                  bordered
                />
                {user.role !== "ADMIN" && (
                  <IconButton
                    text="删除"
                    onClick={() => deleteUser(user.id)}
                    bordered
                  />
                )}
              </div>
            </ListItem>
          ))}
        </List>
      </div>

      {/* 分页 */}
      <div className={styles.pagination}>
        <IconButton
          text="上一页"
          onClick={() =>
            setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
          }
          disabled={pagination.page <= 1}
          bordered
        />
        <span className={styles.pageInfo}>
          第 {pagination.page} 页，共 {pagination.pages} 页
        </span>
        <IconButton
          text="下一页"
          onClick={() =>
            setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
          }
          disabled={pagination.page >= pagination.pages}
          bordered
        />
      </div>

      {/* 用户详情Modal */}
      {selectedUser && (
        <div className="modal-mask" onClick={() => setSelectedUser(null)}>
          <UserDetailModal
            user={selectedUser}
            onClose={() => setSelectedUser(null)}
          />
        </div>
      )}

      {/* 创建用户Modal */}
      {showCreateModal && (
        <div className="modal-mask" onClick={() => setShowCreateModal(false)}>
          <CreateUserModal
            onClose={() => setShowCreateModal(false)}
            onSuccess={() => {
              setShowCreateModal(false);
              fetchUsers();
            }}
          />
        </div>
      )}

      {/* 编辑用户Modal */}
      {editingUser && (
        <div
          className="modal-mask"
          onClick={(e) => e.target === e.currentTarget && setEditingUser(null)}
        >
          <EditUserModal
            user={editingUser}
            onClose={() => setEditingUser(null)}
            onSuccess={() => {
              setEditingUser(null);
              fetchUsers();
            }}
          />
        </div>
      )}
    </div>
  );
}

// 用户详情Modal组件
function UserDetailModal({
  user,
  onClose,
}: {
  user: User;
  onClose: () => void;
}) {
  const formatNumber = (num: number) => num.toLocaleString();
  const formatPoints = (points: number | undefined) => {
    if (points === undefined || points === null) {
      return "☁️0.00 点数";
    }
    return `☁️${points.toFixed(2)} 点数`;
  };
  const formatDate = (dateString: string) =>
    new Date(dateString).toLocaleString("zh-CN");

  return (
    <Modal
      title={`用户详情 - ${user.username}`}
      onClose={onClose}
      actions={[
        <IconButton
          key="close"
          text="关闭"
          onClick={onClose}
          bordered
          shadow
        />,
      ]}
    >
      <List>
        <ListItem title="用户名">
          <span>{user.username}</span>
        </ListItem>
        <ListItem title="邮箱">
          <span>{user.email}</span>
        </ListItem>
        <ListItem title="角色">
          <span>{user.role === "ADMIN" ? "👑 管理员" : "👤 普通用户"}</span>
        </ListItem>
        <ListItem title="状态">
          <span>{user.enabled ? "✅ 已启用" : "❌ 已禁用"}</span>
        </ListItem>
        <ListItem title="总Token使用量">
          <span>{formatNumber(user.totalTokensUsed)}</span>
        </ListItem>
        <ListItem title="总点数消耗">
          <span>{formatPoints(user.totalCost)}</span>
        </ListItem>
        <ListItem title="月度Token使用量">
          <span>{formatNumber(user.monthlyTokensUsed)}</span>
        </ListItem>
        <ListItem title="月度点数消耗">
          <span>{formatPoints(user.monthlyCost)}</span>
        </ListItem>
        <ListItem title="点数余额">
          <span>{formatPoints(user.pointsBalance || 0)}</span>
        </ListItem>
        <ListItem title="API调用次数">
          <span>{formatNumber(user._count.apiLogs)}</span>
        </ListItem>
        <ListItem title="会话数量">
          <span>{formatNumber(user._count.sessions)}</span>
        </ListItem>
        <ListItem title="创建时间">
          <span>{formatDate(user.createdAt)}</span>
        </ListItem>
        <ListItem title="更新时间">
          <span>{formatDate(user.updatedAt)}</span>
        </ListItem>
        {user.lastResetAt && (
          <ListItem title="上次重置时间">
            <span>{formatDate(user.lastResetAt)}</span>
          </ListItem>
        )}
      </List>
    </Modal>
  );
}

// 创建用户Modal组件
function CreateUserModal({
  onClose,
  onSuccess,
}: {
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    role: "USER" as "USER" | "ADMIN",
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!formData.username || !formData.email || !formData.password) {
      showToast("请填写所有必填字段");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "创建用户失败");
      }

      showToast("创建用户成功");
      onSuccess();
    } catch (err) {
      showToast(err instanceof Error ? err.message : "创建用户失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="创建新用户"
      onClose={onClose}
      actions={[
        <IconButton
          key="cancel"
          text="取消"
          onClick={onClose}
          bordered
          shadow
        />,
        <IconButton
          key="create"
          text={loading ? "创建中..." : "创建"}
          onClick={handleSubmit}
          disabled={loading}
          type="primary"
          bordered
          shadow
        />,
      ]}
    >
      <List>
        <ListItem title="用户名 *">
          <input
            type="text"
            value={formData.username}
            onChange={(e) =>
              setFormData({ ...formData, username: e.target.value })
            }
            placeholder="请输入用户名"
            className={styles["text-input"]}
          />
        </ListItem>
        <ListItem title="邮箱 *">
          <input
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            placeholder="请输入邮箱"
            className={styles["text-input"]}
          />
        </ListItem>
        <ListItem title="密码 *">
          <input
            type="password"
            value={formData.password}
            onChange={(e) =>
              setFormData({ ...formData, password: e.target.value })
            }
            placeholder="请输入密码"
            className={styles["text-input"]}
          />
        </ListItem>
        <ListItem title="角色">
          <select
            value={formData.role}
            onChange={(e) =>
              setFormData({
                ...formData,
                role: e.target.value as "USER" | "ADMIN",
              })
            }
            className={styles["select-input"]}
          >
            <option value="USER">普通用户</option>
            <option value="ADMIN">管理员</option>
          </select>
        </ListItem>
      </List>
    </Modal>
  );
}

// 编辑用户Modal组件
function EditUserModal({
  user,
  onClose,
  onSuccess,
}: {
  user: User;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [formData, setFormData] = useState({
    username: user.username,
    email: user.email,
    role: user.role,
    enabled: user.enabled,
    pointsBalance: user.pointsBalance || 0,
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!formData.username || !formData.email) {
      showToast("请填写用户名和邮箱");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "更新用户失败");
      }

      showToast("更新用户成功");
      onSuccess();
    } catch (err) {
      showToast(err instanceof Error ? err.message : "更新用户失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={`编辑用户 - ${user.username}`}
      onClose={onClose}
      actions={[
        <IconButton
          key="cancel"
          text="取消"
          onClick={onClose}
          bordered
          shadow
        />,
        <IconButton
          key="submit"
          text={loading ? "更新中..." : "更新"}
          onClick={handleSubmit}
          type="primary"
          shadow
          disabled={loading}
        />,
      ]}
    >
      <List>
        <ListItem title="用户名">
          <input
            type="text"
            value={formData.username}
            onChange={(e) =>
              setFormData({ ...formData, username: e.target.value })
            }
            placeholder="请输入用户名"
            className={styles["text-input"]}
          />
        </ListItem>
        <ListItem title="邮箱">
          <input
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            placeholder="请输入邮箱"
            className={styles["text-input"]}
          />
        </ListItem>
        <ListItem title="角色">
          <select
            value={formData.role}
            onChange={(e) =>
              setFormData({
                ...formData,
                role: e.target.value as "USER" | "ADMIN",
              })
            }
            className={styles["select-input"]}
          >
            <option value="USER">普通用户</option>
            <option value="ADMIN">管理员</option>
          </select>
        </ListItem>
        <ListItem title="状态">
          <select
            value={formData.enabled ? "true" : "false"}
            onChange={(e) =>
              setFormData({ ...formData, enabled: e.target.value === "true" })
            }
            className={styles["select-input"]}
          >
            <option value="true">启用</option>
            <option value="false">禁用</option>
          </select>
        </ListItem>
        <ListItem title="点数余额">
          <input
            type="number"
            step="0.01"
            min="0"
            value={formData.pointsBalance}
            onChange={(e) =>
              setFormData({
                ...formData,
                pointsBalance: parseFloat(e.target.value) || 0,
              })
            }
            placeholder="请输入点数余额"
            className={styles["text-input"]}
          />
        </ListItem>
      </List>
    </Modal>
  );
}
