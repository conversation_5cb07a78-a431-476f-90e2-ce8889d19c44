.container {
  padding: 0;
  background: transparent;
  min-height: auto;
  position: relative;
  z-index: 1;
}

.header {
  margin-bottom: 0;
  padding: 20px 24px;
  background: var(--second);
  border-radius: 0;
  box-shadow: none;
  border: none;
  border-bottom: var(--border-in-light);

  h2 {
    margin: 0;
    color: var(--black);
    font-size: 20px;
    font-weight: 600;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '📊';
      font-size: 16px;
      -webkit-text-fill-color: initial;
    }
  }
}

.loading {
  text-align: center;
  padding: 40px;
  color: var(--black);
  opacity: 0.7;
  font-size: 16px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border: none;

  &::before {
    content: '⏳';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.7;
  }
}

.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 12px;

  &::before {
    content: '⚠️';
    font-size: 20px;
  }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.statCard {
  background: var(--white);
  padding: 24px;
  border-radius: 16px;
  text-align: center;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-hover));
  }

  &:hover {
    border-color: var(--primary-light);
  }
}

.statValue {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-family: 'Monaco', 'Menlo', monospace;
}

.statLabel {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  align-items: center;
  padding: 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.filterInput,
.filterSelect {
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 14px;
  background: var(--gray-50);
  color: var(--text-primary);
  transition: all 0.2s ease;

  &::placeholder {
    color: var(--text-muted);
  }

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.1);
    background: var(--white);
  }
}

.filterInput {
  min-width: 150px;
}

.filterSelect {
  cursor: pointer;
  min-width: 120px;
}

.clearButton {
  background: linear-gradient(135deg, var(--text-secondary), var(--text-muted));
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '🗑️';
    font-size: 14px;
  }

  &:hover {
    background: var(--gray-600);
  }
}

.tableContainer {
  overflow-x: auto;
  border: 1px solid var(--border-color);
  border-radius: 16px;
  margin-bottom: 20px;
  background: var(--white);
  box-shadow: var(--card-shadow);
  position: relative;
  z-index: 2;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
  background: var(--white);

  th {
    background: var(--gray-50);
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 3;
  }

  td {
    padding: 10px 8px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: top;
    background: var(--white);
  }

  tr:hover td {
    background: var(--gray-50);
  }
}

.subText {
  font-size: 11px;
  color: var(--gray-500);
  margin-top: 2px;
}

.anonymous {
  color: var(--gray-500);
  font-style: italic;
  font-size: 12px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;

  &.success {
    background: #dcfce7;
    color: #166534;
  }

  &.error {
    background: #fee2e2;
    color: #dc2626;
  }

  &.timeout {
    background: #fef3c7;
    color: #92400e;
  }
}

.errorMsg {
  color: var(--red);
  font-size: 11px;
  max-width: 200px;
  word-break: break-word;
  cursor: help;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.pageButton {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--primary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.pageInfo {
  color: var(--gray-600);
  font-size: 14px;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .statCard {
    padding: 12px;
  }

  .statValue {
    font-size: 20px;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  .filterInput,
  .filterSelect {
    min-width: auto;
  }

  .table {
    font-size: 11px;

    th,
    td {
      padding: 6px 4px;
    }
  }

  .subText {
    font-size: 10px;
  }

  .errorMsg {
    max-width: 100px;
  }
}

@media (max-width: 480px) {
  .statsGrid {
    grid-template-columns: 1fr;
  }

  .table {
    th,
    td {
      padding: 4px 2px;
    }
  }
}
