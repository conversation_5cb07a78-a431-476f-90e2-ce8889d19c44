.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 24px;
  
  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--black);
  }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.statCard {
  background: var(--white);
  border: 1px solid var(--border-in-light);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
  }
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  span:first-child {
    color: var(--text-secondary);
    font-size: 14px;
  }
  
  .success {
    color: #27ae60;
    font-weight: 600;
  }
  
  .failed {
    color: #e74c3c;
    font-weight: 600;
  }
}

.filters {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--white);
  border: 1px solid var(--border-in-light);
  border-radius: 12px;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  label {
    font-size: 14px;
    font-weight: 500;
    color: var(--black);
  }
  
  select,
  input {
    padding: 8px 12px;
    border: 1px solid var(--border-in-light);
    border-radius: 6px;
    font-size: 14px;
    background: var(--white);
    color: var(--black);
    
    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }
  
  select {
    min-width: 120px;
  }
  
  input[type="date"] {
    min-width: 140px;
  }
}

.filterButton {
  padding: 8px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background: var(--primary-dark, #2980b9);
  }
}

.tableContainer {
  background: var(--white);
  border: 1px solid var(--border-in-light);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.loading {
  padding: 40px;
  text-align: center;
  color: var(--text-secondary);
}

.table {
  width: 100%;
  border-collapse: collapse;
  
  th,
  td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-in-light);
  }
  
  th {
    background: var(--second);
    font-weight: 600;
    color: var(--black);
    font-size: 14px;
  }
  
  td {
    font-size: 14px;
    color: var(--black);
  }
  
  tbody tr:hover {
    background: var(--hover-color);
  }
}

.orderNo {
  font-family: monospace;
  font-size: 12px;
  color: var(--text-secondary);
}

.userInfo {
  .email {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 2px;
  }
}

.amount {
  font-weight: 600;
  color: var(--primary);
}

.status {
  font-weight: 500;
  font-size: 13px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  
  button {
    padding: 8px 16px;
    border: 1px solid var(--border-in-light);
    background: var(--white);
    color: var(--black);
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    
    &:hover:not(:disabled) {
      background: var(--hover-color);
      border-color: var(--primary);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  span {
    font-size: 14px;
    color: var(--text-secondary);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filterGroup {
    flex-direction: row;
    align-items: center;
    
    label {
      min-width: 80px;
    }
  }
  
  .table {
    font-size: 12px;
    
    th,
    td {
      padding: 8px 12px;
    }
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
}
