"use client";

import React, { useState, useEffect } from "react";
import { useUserStore } from "@/app/store/user";
import styles from "./payment-management.module.scss";

interface PaymentOrder {
  id: string;
  outTradeNo: string;
  amount: number;
  status: string;
  payChannel: string;
  tradeType: string;
  createdAt: string;
  successTime?: string;
  expireTime?: string;
  user: {
    id: string;
    username: string;
    email: string;
  };
}

interface PaymentStats {
  all: Record<string, { count: number; amount: number }>;
  today: Record<string, { count: number; amount: number }>;
}

const STATUS_MAP = {
  PENDING: "待支付",
  PAID: "已支付",
  FAILED: "支付失败",
  EXPIRED: "已过期",
  CANCELLED: "已取消",
};

const STATUS_COLORS = {
  PENDING: "#f39c12",
  PAID: "#27ae60",
  FAILED: "#e74c3c",
  EXPIRED: "#95a5a6",
  CANCELLED: "#95a5a6",
};

export function PaymentManagement() {
  const userStore = useUserStore();
  const [orders, setOrders] = useState<PaymentOrder[]>([]);
  const [stats, setStats] = useState<PaymentStats>({ all: {}, today: {} });
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [dateRange, setDateRange] = useState({ start: "", end: "" });

  const fetchPayments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
      });

      if (statusFilter !== "ALL") {
        params.append("status", statusFilter);
      }

      if (dateRange.start) {
        params.append("startDate", dateRange.start);
      }

      if (dateRange.end) {
        params.append("endDate", dateRange.end);
      }

      const response = await fetch(`/api/admin/payments?${params}`, {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      });

      const result = await response.json();

      if (result.success) {
        setOrders(result.data.orders);
        setStats(result.data.stats);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        console.error("Failed to fetch payments:", result.error);
      }
    } catch (error) {
      console.error("Fetch payments error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPayments();
  }, [currentPage, statusFilter, dateRange]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const formatAmount = (amount: number) => {
    return `¥${amount.toFixed(2)}`;
  };

  const getStatsValue = (
    statsObj: Record<string, any>,
    status: string,
    field: "count" | "amount",
  ) => {
    return statsObj[status]?.[field] || 0;
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>充值管理</h2>
      </div>

      {/* 统计卡片 */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3>今日统计</h3>
          <div className={styles.statItem}>
            <span>成功充值：</span>
            <span className={styles.success}>
              {getStatsValue(stats.today, "PAID", "count")} 笔 /{" "}
              {formatAmount(getStatsValue(stats.today, "PAID", "amount"))}
            </span>
          </div>
          <div className={styles.statItem}>
            <span>失败充值：</span>
            <span className={styles.failed}>
              {getStatsValue(stats.today, "FAILED", "count")} 笔 /{" "}
              {formatAmount(getStatsValue(stats.today, "FAILED", "amount"))}
            </span>
          </div>
        </div>

        <div className={styles.statCard}>
          <h3>总体统计</h3>
          <div className={styles.statItem}>
            <span>成功充值：</span>
            <span className={styles.success}>
              {getStatsValue(stats.all, "PAID", "count")} 笔 /{" "}
              {formatAmount(getStatsValue(stats.all, "PAID", "amount"))}
            </span>
          </div>
          <div className={styles.statItem}>
            <span>失败充值：</span>
            <span className={styles.failed}>
              {getStatsValue(stats.all, "FAILED", "count")} 笔 /{" "}
              {formatAmount(getStatsValue(stats.all, "FAILED", "amount"))}
            </span>
          </div>
        </div>
      </div>

      {/* 筛选器 */}
      <div className={styles.filters}>
        <div className={styles.filterGroup}>
          <label>状态筛选：</label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="ALL">全部</option>
            {Object.entries(STATUS_MAP).map(([key, label]) => (
              <option key={key} value={key}>
                {label}
              </option>
            ))}
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label>开始日期：</label>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) =>
              setDateRange((prev) => ({ ...prev, start: e.target.value }))
            }
          />
        </div>

        <div className={styles.filterGroup}>
          <label>结束日期：</label>
          <input
            type="date"
            value={dateRange.end}
            onChange={(e) =>
              setDateRange((prev) => ({ ...prev, end: e.target.value }))
            }
          />
        </div>

        <button
          onClick={() => setCurrentPage(1)}
          className={styles.filterButton}
        >
          筛选
        </button>
      </div>

      {/* 订单列表 */}
      <div className={styles.tableContainer}>
        {loading ? (
          <div className={styles.loading}>加载中...</div>
        ) : (
          <table className={styles.table}>
            <thead>
              <tr>
                <th>订单号</th>
                <th>用户</th>
                <th>金额</th>
                <th>状态</th>
                <th>支付方式</th>
                <th>创建时间</th>
                <th>完成时间</th>
              </tr>
            </thead>
            <tbody>
              {orders.map((order) => (
                <tr key={order.id}>
                  <td className={styles.orderNo}>{order.outTradeNo}</td>
                  <td>
                    <div className={styles.userInfo}>
                      <div>{order.user.username}</div>
                      <div className={styles.email}>{order.user.email}</div>
                    </div>
                  </td>
                  <td className={styles.amount}>
                    {formatAmount(order.amount)}
                  </td>
                  <td>
                    <span
                      className={styles.status}
                      style={{
                        color:
                          STATUS_COLORS[
                            order.status as keyof typeof STATUS_COLORS
                          ],
                      }}
                    >
                      {STATUS_MAP[order.status as keyof typeof STATUS_MAP]}
                    </span>
                  </td>
                  <td>
                    {order.payChannel === "wxpay"
                      ? "微信支付"
                      : order.payChannel}
                  </td>
                  <td>{formatDate(order.createdAt)}</td>
                  <td>
                    {order.successTime ? formatDate(order.successTime) : "-"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* 分页 */}
      {totalPages > 1 && (
        <div className={styles.pagination}>
          <button
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            上一页
          </button>
          <span>
            第 {currentPage} 页，共 {totalPages} 页
          </span>
          <button
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            下一页
          </button>
        </div>
      )}
    </div>
  );
}
