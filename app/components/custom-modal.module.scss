.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modalContent {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
  border: 1px solid var(--border-in-light);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-in-light);
  background: var(--white);
  flex-shrink: 0;
}

.modalTitle {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
}

.closeButton {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: var(--text-secondary);
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--hover-color);
    color: var(--black);
  }

  &:active {
    transform: scale(0.95);
  }

  svg {
    width: 20px;
    height: 20px;
  }
}

.modalBody {
  padding: 0;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }

  .modalContent {
    width: 100% !important;
    max-width: 100% !important;
    max-height: 95vh;
    border-radius: 12px;
  }

  .modalHeader {
    padding: 16px 20px;
  }

  .modalTitle {
    font-size: 16px;
  }
}
