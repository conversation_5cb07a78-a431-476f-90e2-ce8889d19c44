// 自定义Modal样式
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.custom-modal {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 90vw;
  max-width: 1200px;
  height: 85vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-in-light);
  background: var(--white);
  flex-shrink: 0;

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--black);
    margin: 0;
  }

  .modal-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .close-button {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--second);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;

    &:hover {
      background: var(--hover-color);
      color: var(--black);
    }
  }
}

.modal-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 24px;
  overflow: hidden;
}

.date-filter {
  display: flex;
  gap: 16px;
  padding: 12px 16px;
  background: var(--second);
  border-radius: 12px;
  border: 1px solid var(--border-in-light);
  flex-shrink: 0; // 防止被压缩

  .date-input-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;

    label {
      font-size: 12px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .date-input {
      padding: 6px 10px;
      border: 1px solid var(--border-in-light);
      border-radius: 6px;
      background: var(--white);
      font-size: 13px;
      color: var(--black);
      transition: border-color 0.2s ease;
      width: 100%;

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }
  }
}

.logs-container {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0; // 确保flex子元素可以收缩
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--second);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-in-light);
    border-radius: 3px;

    &:hover {
      background: var(--text-secondary);
    }
  }
}

.loading,
.empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  font-size: 14px;
}

.log-item {
  background: var(--white);
  border: 1px solid var(--border-in-light);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .log-model {
    display: flex;
    align-items: center;
    gap: 8px;

    .model-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--black);
    }

    .provider-name {
      font-size: 11px;
      color: var(--text-secondary);
      background: var(--second);
      padding: 2px 6px;
      border-radius: 4px;
      display: inline-block;
      white-space: nowrap;
    }
  }

  .log-status {
    font-size: 14px;
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.05);
    white-space: nowrap;
  }
}

.log-details {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 2fr; // 优化列宽比例：Token使用(2) 消耗(1) 耗时(1) 时间(2)
  gap: 8px;

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: var(--second);
    border-radius: 6px;
    min-width: 0; // 允许内容收缩

    .detail-label {
      font-size: 11px;
      color: var(--text-secondary);
      font-weight: 500;
      white-space: nowrap;
      margin-right: 4px;
    }

    .detail-value {
      font-size: 13px;
      color: var(--black);
      font-weight: 600;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .token-breakdown {
        font-size: 11px;
        color: var(--text-secondary);
        font-weight: 400;
        margin-left: 2px;
      }
    }

    // 针对不同类型的详情项优化
    &:nth-child(1) { // Token使用
      .detail-value {
        font-size: 14px;
      }
    }

    &:nth-child(2), &:nth-child(3) { // 消耗和耗时
      .detail-label {
        font-size: 10px;
      }
      .detail-value {
        font-size: 12px;
      }
    }

    &:nth-child(4) { // 时间
      .detail-value {
        font-size: 12px;
      }
    }
  }
}

.error-message {
  margin-top: 12px;
  padding: 12px;
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.2);
  border-radius: 8px;
  color: #d32f2f;
  font-size: 13px;
  line-height: 1.4;
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 16px;
}

@media (max-width: 768px) {
  .custom-modal {
    width: 95vw;
    height: 90vh;
  }

  .modal-content {
    padding: 12px 16px;
  }

  .date-filter {
    flex-direction: column;
    gap: 12px;
  }

  .log-details {
    grid-template-columns: 1fr 1fr; // 移动端显示2列
    gap: 6px;
  }

  .log-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .log-model {
      width: 100%;
      flex-wrap: wrap;
    }

    .log-status {
      align-self: flex-end;
    }
  }
}

@media (max-width: 480px) {
  .custom-modal {
    width: 98vw;
    height: 95vh;
  }

  .modal-header {
    padding: 12px 16px;

    .modal-title {
      font-size: 16px;
    }
  }

  .modal-content {
    padding: 8px 12px;
  }

  .log-details {
    grid-template-columns: 1fr; // 小屏幕显示1列
    gap: 4px;

    .detail-item {
      padding: 4px 8px;

      .detail-label {
        font-size: 10px;
      }

      .detail-value {
        font-size: 11px;
      }
    }
  }

  .log-header {
    .log-model {
      .model-name {
        font-size: 14px;
      }

      .provider-name {
        font-size: 10px;
      }
    }
  }
}
