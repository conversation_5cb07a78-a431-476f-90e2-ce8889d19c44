import { useState, useEffect } from "react";
import { useUserStore } from "../store/user";
import { Modal, showToast } from "./ui-lib";
import { IconButton } from "./button";
import styles from "./user-history-logs.module.scss";

interface ApiLog {
  id: string;
  model: string;
  tokensUsed: number;
  promptTokens: number;
  completionTokens: number;
  cost: number;
  status: "success" | "error" | "timeout";
  errorMsg?: string;
  duration?: number;
  createdAt: string;
  provider: {
    name: string;
    type: string;
  };
}

interface UserHistoryLogsProps {
  visible: boolean;
  onClose: () => void;
}

export function UserHistoryLogs({ visible, onClose }: UserHistoryLogsProps) {
  const userStore = useUserStore();
  const [logs, setLogs] = useState<ApiLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0], // 默认显示最近7天
    endDate: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    if (visible && userStore.isAuthenticated) {
      loadHistoryLogs(true);
    }
  }, [visible, dateRange]);

  const loadHistoryLogs = async (reset = false) => {
    try {
      setLoading(true);
      const currentPage = reset ? 1 : page;
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      });

      const response = await fetch(`/api/user/history?${params}`, {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      });

      const result = await response.json();
      if (result.success) {
        if (reset) {
          setLogs(result.logs);
          setPage(2);
        } else {
          setLogs((prev) => [...prev, ...result.logs]);
          setPage((prev) => prev + 1);
        }
        setHasMore(result.logs.length === 20);
      } else {
        showToast(result.error || "获取使用历史失败");
      }
    } catch (error) {
      console.error("Load history logs error:", error);
      showToast("获取使用历史失败");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const formatCost = (cost: number) => {
    return `☁️${cost.toFixed(4)}`;
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return "-";
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "#4caf50";
      case "error":
        return "#f44336";
      case "timeout":
        return "#ff9800";
      default:
        return "#9e9e9e";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "success":
        return "成功";
      case "error":
        return "失败";
      case "timeout":
        return "超时";
      default:
        return "未知";
    }
  };

  if (!visible) return null;

  return (
    <div
      className={styles["modal-mask"]}
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div className={styles["custom-modal"]}>
        {/* 自定义头部 */}
        <div className={styles["modal-header"]}>
          <h2 className={styles["modal-title"]}>使用历史记录</h2>
          <div className={styles["modal-actions"]}>
            <button className={styles["close-button"]} onClick={onClose}>
              ✕
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className={styles["modal-content"]}>
          {/* 日期筛选 */}
          <div className={styles["date-filter"]}>
            <div className={styles["date-input-group"]}>
              <label>开始日期:</label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) =>
                  setDateRange({ ...dateRange, startDate: e.target.value })
                }
                className={styles["date-input"]}
              />
            </div>
            <div className={styles["date-input-group"]}>
              <label>结束日期:</label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) =>
                  setDateRange({ ...dateRange, endDate: e.target.value })
                }
                className={styles["date-input"]}
              />
            </div>
          </div>

          {/* 历史记录列表 */}
          <div className={styles["logs-container"]}>
            {loading && logs.length === 0 ? (
              <div className={styles["loading"]}>加载中...</div>
            ) : logs.length === 0 ? (
              <div className={styles["empty"]}>暂无使用记录</div>
            ) : (
              <>
                {logs.map((log) => (
                  <div key={log.id} className={styles["log-item"]}>
                    <div className={styles["log-header"]}>
                      <div className={styles["log-model"]}>
                        <span className={styles["model-name"]}>
                          {log.model}
                        </span>
                        <span className={styles["provider-name"]}>
                          {log.provider.name}
                        </span>
                      </div>
                      <div
                        className={styles["log-status"]}
                        style={{ color: getStatusColor(log.status) }}
                      >
                        {getStatusText(log.status)}
                      </div>
                    </div>

                    <div className={styles["log-details"]}>
                      <div className={styles["detail-item"]}>
                        <span className={styles["detail-label"]}>
                          Token使用:
                        </span>
                        <span className={styles["detail-value"]}>
                          {log.tokensUsed?.toLocaleString() || 0}
                          {log.promptTokens && log.completionTokens && (
                            <span className={styles["token-breakdown"]}>
                              ({log.promptTokens}+{log.completionTokens})
                            </span>
                          )}
                        </span>
                      </div>

                      <div className={styles["detail-item"]}>
                        <span className={styles["detail-label"]}>消耗:</span>
                        <span className={styles["detail-value"]}>
                          {formatCost(log.cost || 0)}
                        </span>
                      </div>

                      <div className={styles["detail-item"]}>
                        <span className={styles["detail-label"]}>耗时:</span>
                        <span className={styles["detail-value"]}>
                          {formatDuration(log.duration)}
                        </span>
                      </div>

                      <div className={styles["detail-item"]}>
                        <span className={styles["detail-label"]}>时间:</span>
                        <span className={styles["detail-value"]}>
                          {formatDate(log.createdAt)}
                        </span>
                      </div>
                    </div>

                    {log.status === "error" && log.errorMsg && (
                      <div className={styles["error-message"]}>
                        错误信息: {log.errorMsg}
                      </div>
                    )}
                  </div>
                ))}

                {hasMore && (
                  <div className={styles["load-more"]}>
                    <IconButton
                      text={loading ? "加载中..." : "加载更多"}
                      onClick={() => loadHistoryLogs(false)}
                      disabled={loading}
                      bordered
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
