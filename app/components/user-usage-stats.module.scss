.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fade-in 0.2s ease;
}

.modal {
  background: var(--white);
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--card-shadow);
  animation: slide-up 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: var(--border-in-light);

  h2 {
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
    margin: 0;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      color: var(--black);
      background: var(--hover-color);
    }
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-body {
  padding: 20px;
}

.date-selector {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--second);
  border-radius: 10px;
  align-items: flex-end;
}

.date-item {
  flex: 1;
  min-width: 0; // 防止 flex 子项溢出

  label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: var(--black);
    margin-bottom: 6px;
  }

  input {
    width: 100%;
    padding: 10px 12px;
    border: var(--border-in-light);
    border-radius: 8px;
    font-size: 13px;
    background: var(--white);
    color: var(--black);
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 2px rgba(29, 147, 171, 0.1);
    }
  }
}

.loading {
  text-align: center;
  padding: 40px;
  color: var(--text-secondary);
  font-size: 14px;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: var(--text-secondary);
  font-size: 14px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-overview {
  padding: 16px;
  background: var(--second);
  border-radius: 10px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--black);
    margin: 0;
  }

  .date-range {
    font-size: 12px;
    color: var(--text-secondary);
    background: var(--hover-color);
    padding: 4px 8px;
    border-radius: 6px;
  }
}

.usage-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  .summary-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
  }

  .summary-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.stat-card {
  padding: 12px;
  background: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
  text-align: center;
}

.stat-title {
  font-size: 11px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--black);
}

.section {
  h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 12px 0;
  }
}

.models-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
}

.model-rank {
  font-size: 12px;
  font-weight: 600;
  color: var(--primary);
  min-width: 20px;
}

.model-info {
  flex: 1;
}

.model-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 2px;
}

.model-stats {
  font-size: 10px;
  color: var(--text-secondary);
}

.daily-chart {
  display: flex;
  align-items: flex-end;
  gap: 6px;
  height: 100px;
  padding: 12px;
  background: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
}

.day-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  height: 100%;
}

.day-date {
  font-size: 10px;
  color: var(--text-secondary);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.day-bar {
  flex: 1;
  width: 16px;
  background: var(--hover-color);
  border-radius: 2px;
  display: flex;
  align-items: flex-end;
  overflow: hidden;
}

.day-fill {
  width: 100%;
  background: var(--primary);
  border-radius: 2px;
  min-height: 2px;
  transition: height 0.3s ease;
}

.day-value {
  font-size: 10px;
  font-weight: 500;
  color: var(--black);
}

.billing-info {
  background: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
  overflow: hidden;
}

.billing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-bottom: var(--border-in-light);

  &:last-child {
    border-bottom: none;
  }

  span:first-child {
    font-size: 12px;
    color: var(--text-secondary);
  }

  span:last-child {
    font-size: 12px;
    font-weight: 500;
    color: var(--black);
  }

  .total-fee {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary) !important;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modal {
    width: 95%;
    max-height: 95vh;
  }

  .date-selector {
    flex-direction: column;
    gap: 8px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .daily-chart {
    height: 80px;
    padding: 8px;
  }

  .day-bar {
    width: 12px;
  }
}
