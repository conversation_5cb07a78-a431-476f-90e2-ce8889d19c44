import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import { useUserStore } from "../store/user";
import { useAppConfig, Theme } from "../store/config";
import LeftIcon from "@/app/icons/left.svg";
import LightIcon from "../icons/light.svg";
import DarkIcon from "../icons/dark.svg";
import AutoIcon from "../icons/auto.svg";

type AuthMode = "login" | "register";

export function UserAuthPage() {
  const navigate = useNavigate();
  const userStore = useUserStore();
  const config = useAppConfig();
  const [mode, setMode] = useState<AuthMode>("login");
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  // 添加CSS动画样式和响应式设计
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes grid-move {
        0% { transform: translate(0, 0); }
        100% { transform: translate(50px, 50px); }
      }
      @keyframes fade-in {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .auth-container-animation {
        animation: fade-in 0.6s ease-out;
      }
      .auth-header-animation {
        animation: fade-in 0.4s ease-out 0.2s both;
      }

      /* 响应式设计 */
      @media only screen and (max-width: 600px) {
        .auth-main-container {
          width: 100vw !important;
          height: 100vh !important;
          min-width: unset !important;
          min-height: unset !important;
          border-radius: 0 !important;
          border: 0 !important;
        }
        .auth-form-container {
          max-width: 100% !important;
          padding: 20px !important;
        }
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const goHome = () => navigate(Path.Home);

  // 主题切换功能
  const theme = config.theme;

  function nextTheme() {
    const themes = [Theme.Auto, Theme.Light, Theme.Dark];
    const themeIndex = themes.indexOf(theme);
    const nextIndex = (themeIndex + 1) % themes.length;
    const nextTheme = themes[nextIndex];
    config.update((config) => (config.theme = nextTheme));
  }

  function getThemeIcon() {
    switch (theme) {
      case Theme.Light:
        return <LightIcon />;
      case Theme.Dark:
        return <DarkIcon />;
      default:
        return <AutoIcon />;
    }
  }

  // 应用主题到页面
  useEffect(() => {
    const applyTheme = () => {
      document.body.classList.remove("light", "dark");

      if (theme === "light") {
        document.body.classList.add("light");
        document.body.setAttribute("data-theme", "light");
      } else if (theme === "dark") {
        document.body.classList.add("dark");
        document.body.setAttribute("data-theme", "dark");
      } else {
        // auto theme
        const isDark = window.matchMedia(
          "(prefers-color-scheme: dark)",
        ).matches;
        document.body.classList.add(isDark ? "dark" : "light");
        document.body.setAttribute("data-theme", isDark ? "dark" : "light");
      }
    };

    applyTheme();

    // 监听系统主题变化
    if (theme === "auto") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = () => applyTheme();
      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }
  }, [theme]);

  // 如果已经登录，直接跳转到聊天页面
  useEffect(() => {
    if (userStore.isAuthenticated) {
      navigate(Path.Chat);
    }
  }, [userStore.isAuthenticated, navigate]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    if (mode === "login") {
      if (!formData.username || !formData.password) {
        userStore.setError("请填写用户名和密码");
        return;
      }

      const success = await userStore.login(
        formData.username,
        formData.password,
      );
      if (success) {
        navigate(Path.Chat);
      }
    } else {
      // 注册模式
      if (!formData.username || !formData.email || !formData.password) {
        userStore.setError("请填写所有必填字段");
        return;
      }

      if (formData.password !== formData.confirmPassword) {
        userStore.setError("两次输入的密码不一致");
        return;
      }

      if (formData.password.length < 6) {
        userStore.setError("密码长度至少6位");
        return;
      }

      const success = await userStore.register(
        formData.username,
        formData.email,
        formData.password,
      );
      if (success) {
        navigate(Path.Chat);
      }
    }
  };

  const switchMode = () => {
    setMode(mode === "login" ? "register" : "login");
    setFormData({
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
    });
    userStore.setError(null);
  };

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        background: theme === "light" ? "#f8fafc" : "#0a0a0a",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        padding: "20px",
        boxSizing: "border-box",
        transition: "background-color 0.3s ease",
      }}
    >
      {/* 背景装饰 */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 0,
          backgroundImage:
            theme === "light"
              ? "linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px)"
              : "linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px)",
          backgroundSize: "50px 50px",
          animation: "grid-move 20s linear infinite",
        }}
      />

      {/* 主窗口容器 - 模仿聊天界面的窗口设计 */}
      <div
        className="auth-container-animation auth-main-container"
        style={{
          position: "relative",
          zIndex: 1,
          width: "90vw",
          height: "90vh",
          maxWidth: "1200px",
          minWidth: "600px",
          minHeight: "370px",
          background: theme === "light" ? "white" : "var(--white)",
          border:
            theme === "light"
              ? "1px solid rgba(0, 0, 0, 0.1)"
              : "var(--border-in-light)",
          borderRadius: "20px",
          boxShadow:
            theme === "light"
              ? "0 20px 40px rgba(0, 0, 0, 0.1)"
              : "50px 50px 100px 10px rgba(0, 0, 0, 0.1)",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
          transition: "all 0.3s ease",
        }}
      >
        {/* 头部区域 - 返回按钮和主题切换 */}
        <div
          className="auth-header-animation"
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "20px 20px 0 20px",
            flexShrink: 0,
          }}
        >
          <button
            onClick={goHome}
            style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              background: theme === "light" ? "var(--white)" : "var(--white)",
              border:
                theme === "light"
                  ? "var(--border-in-light)"
                  : "var(--border-in-light)",
              borderRadius: "10px",
              padding: "8px 16px",
              color: theme === "light" ? "var(--black)" : "var(--black)",
              fontSize: "14px",
              cursor: "pointer",
              boxShadow:
                theme === "light" ? "var(--card-shadow)" : "var(--card-shadow)",
              transition: "all 0.3s ease",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "var(--hover-color)";
              e.currentTarget.style.boxShadow = "var(--card-shadow-hover)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "var(--white)";
              e.currentTarget.style.boxShadow = "var(--card-shadow)";
            }}
          >
            <LeftIcon style={{ width: "16px", height: "16px" }} />
            返回
          </button>

          <button
            onClick={nextTheme}
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: theme === "light" ? "var(--white)" : "var(--white)",
              border:
                theme === "light"
                  ? "var(--border-in-light)"
                  : "var(--border-in-light)",
              borderRadius: "10px",
              padding: "8px",
              color: theme === "light" ? "var(--black)" : "var(--black)",
              cursor: "pointer",
              boxShadow:
                theme === "light" ? "var(--card-shadow)" : "var(--card-shadow)",
              transition: "all 0.3s ease",
              width: "40px",
              height: "40px",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "var(--hover-color)";
              e.currentTarget.style.boxShadow = "var(--card-shadow-hover)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "var(--white)";
              e.currentTarget.style.boxShadow = "var(--card-shadow)";
            }}
            title={`当前主题: ${
              theme === "auto" ? "自动" : theme === "light" ? "浅色" : "深色"
            }`}
          >
            {getThemeIcon()}
          </button>
        </div>

        {/* 主要内容区域 */}
        <div
          style={{
            flex: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "20px",
          }}
        >
          <div
            className="auth-form-container"
            style={{
              width: "100%",
              maxWidth: "400px",
              background: theme === "light" ? "var(--white)" : "var(--white)",
              border:
                theme === "light"
                  ? "var(--border-in-light)"
                  : "var(--border-in-light)",
              borderRadius: "20px",
              boxShadow:
                theme === "light" ? "var(--card-shadow)" : "var(--card-shadow)",
              padding: "40px 32px",
              transition: "all 0.3s ease",
            }}
          >
            <h2
              style={{
                fontSize: "28px",
                fontWeight: "700",
                color: "var(--black)",
                margin: "0 0 8px 0",
                textAlign: "center",
                transition: "color 0.3s ease",
              }}
            >
              {mode === "login" ? "登录 QADChat" : "注册 QADChat"}
            </h2>

            <p
              style={{
                fontSize: "14px",
                color: "var(--black)",
                opacity: "0.7",
                textAlign: "center",
                margin: "0 0 32px 0",
                transition: "color 0.3s ease",
              }}
            >
              {mode === "login"
                ? "请输入您的用户名和密码"
                : "创建您的 QADChat 账户"}
            </p>

            {/* 错误信息显示 */}
            {userStore.error && (
              <div
                style={{
                  background: "rgba(239, 68, 68, 0.1)",
                  border: "1px solid rgba(239, 68, 68, 0.3)",
                  borderRadius: "10px",
                  padding: "12px",
                  color: "#ef4444",
                  fontSize: "14px",
                  marginBottom: "24px",
                  textAlign: "center",
                }}
              >
                {userStore.error}
              </div>
            )}

            {/* 表单字段 */}
            <div style={{ marginBottom: "24px" }}>
              {/* 用户名输入 */}
              <div style={{ marginBottom: "16px" }}>
                <input
                  type="text"
                  placeholder="用户名"
                  value={formData.username}
                  onChange={(e) =>
                    handleInputChange("username", e.target.value)
                  }
                  style={{
                    width: "100%",
                    maxWidth: "none",
                    padding: "16px 20px",
                    background: "var(--white)",
                    border: "var(--border-in-light)",
                    borderRadius: "10px",
                    color: "var(--black)",
                    fontSize: "16px",
                    transition: "all 0.3s ease",
                    boxSizing: "border-box",
                    outline: "none",
                    boxShadow: "var(--card-shadow)",
                    textAlign: "left",
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "var(--primary)";
                    e.target.style.boxShadow =
                      "0 0 0 3px rgba(29, 147, 171, 0.1)";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "var(--border-in-light)";
                    e.target.style.boxShadow = "var(--card-shadow)";
                  }}
                />
              </div>

              {/* 邮箱输入（仅注册时显示） */}
              {mode === "register" && (
                <div style={{ marginBottom: "16px" }}>
                  <input
                    type="email"
                    placeholder="邮箱地址"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    style={{
                      width: "100%",
                      maxWidth: "none",
                      padding: "16px 20px",
                      background: "var(--white)",
                      border: "var(--border-in-light)",
                      borderRadius: "10px",
                      color: "var(--black)",
                      fontSize: "16px",
                      transition: "all 0.3s ease",
                      boxSizing: "border-box",
                      outline: "none",
                      boxShadow: "var(--card-shadow)",
                      textAlign: "left",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "var(--primary)";
                      e.target.style.boxShadow =
                        "0 0 0 3px rgba(29, 147, 171, 0.1)";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "var(--border-in-light)";
                      e.target.style.boxShadow = "var(--card-shadow)";
                    }}
                  />
                </div>
              )}

              {/* 密码输入 */}
              <div style={{ marginBottom: "16px" }}>
                <input
                  type="password"
                  placeholder="密码"
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange("password", e.target.value)
                  }
                  style={{
                    width: "100%",
                    maxWidth: "none",
                    padding: "16px 20px",
                    background: "var(--white)",
                    border: "var(--border-in-light)",
                    borderRadius: "10px",
                    color: "var(--black)",
                    fontSize: "16px",
                    transition: "all 0.3s ease",
                    boxSizing: "border-box",
                    outline: "none",
                    boxShadow: "var(--card-shadow)",
                    textAlign: "left",
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = "var(--primary)";
                    e.target.style.boxShadow =
                      "0 0 0 3px rgba(29, 147, 171, 0.1)";
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = "var(--border-in-light)";
                    e.target.style.boxShadow = "var(--card-shadow)";
                  }}
                />
              </div>

              {/* 确认密码输入（仅注册时显示） */}
              {mode === "register" && (
                <div style={{ marginBottom: "16px" }}>
                  <input
                    type="password"
                    placeholder="确认密码"
                    value={formData.confirmPassword}
                    onChange={(e) =>
                      handleInputChange("confirmPassword", e.target.value)
                    }
                    style={{
                      width: "100%",
                      maxWidth: "none",
                      padding: "16px 20px",
                      background: "var(--white)",
                      border: "var(--border-in-light)",
                      borderRadius: "10px",
                      color: "var(--black)",
                      fontSize: "16px",
                      transition: "all 0.3s ease",
                      boxSizing: "border-box",
                      outline: "none",
                      boxShadow: "var(--card-shadow)",
                      textAlign: "left",
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "var(--primary)";
                      e.target.style.boxShadow =
                        "0 0 0 3px rgba(29, 147, 171, 0.1)";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "var(--border-in-light)";
                      e.target.style.boxShadow = "var(--card-shadow)";
                    }}
                  />
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div style={{ marginBottom: "16px" }}>
              <button
                onClick={handleSubmit}
                disabled={userStore.isLoading}
                style={{
                  width: "100%",
                  padding: "16px",
                  background: "var(--primary)",
                  border: "none",
                  borderRadius: "10px",
                  color: "white",
                  fontSize: "16px",
                  fontWeight: "600",
                  cursor: userStore.isLoading ? "not-allowed" : "pointer",
                  transition: "all 0.3s ease",
                  marginBottom: "16px",
                  opacity: userStore.isLoading ? "0.6" : "1",
                  boxShadow: "var(--card-shadow)",
                }}
                onMouseEnter={(e) => {
                  if (!userStore.isLoading) {
                    e.currentTarget.style.background = "var(--primary-hover)";
                    e.currentTarget.style.transform = "translateY(-1px)";
                    e.currentTarget.style.boxShadow =
                      "var(--card-shadow-hover)";
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = "var(--primary)";
                  e.currentTarget.style.transform = "translateY(0)";
                  e.currentTarget.style.boxShadow = "var(--card-shadow)";
                }}
              >
                {userStore.isLoading
                  ? "处理中..."
                  : mode === "login"
                  ? "登录"
                  : "注册"}
              </button>

              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  margin: "16px 0",
                  position: "relative",
                }}
              >
                <div
                  style={{
                    flex: 1,
                    height: "1px",
                    background: "var(--border-in-light)",
                  }}
                />
                <span
                  style={{
                    padding: "0 16px",
                    color: "var(--black)",
                    opacity: "0.5",
                    fontSize: "14px",
                  }}
                >
                  or
                </span>
                <div
                  style={{
                    flex: 1,
                    height: "1px",
                    background: "var(--border-in-light)",
                  }}
                />
              </div>

              <button
                onClick={switchMode}
                disabled={userStore.isLoading}
                style={{
                  width: "100%",
                  padding: "16px",
                  background: "var(--white)",
                  border: "var(--border-in-light)",
                  borderRadius: "10px",
                  color: "var(--black)",
                  fontSize: "16px",
                  fontWeight: "500",
                  cursor: userStore.isLoading ? "not-allowed" : "pointer",
                  transition: "all 0.3s ease",
                  opacity: userStore.isLoading ? "0.6" : "1",
                  boxShadow: "var(--card-shadow)",
                }}
                onMouseEnter={(e) => {
                  if (!userStore.isLoading) {
                    e.currentTarget.style.background = "var(--hover-color)";
                    e.currentTarget.style.transform = "translateY(-1px)";
                    e.currentTarget.style.boxShadow =
                      "var(--card-shadow-hover)";
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = "var(--white)";
                  e.currentTarget.style.transform = "translateY(0)";
                  e.currentTarget.style.boxShadow = "var(--card-shadow)";
                }}
              >
                {mode === "login" ? "创建新账户" : "已有账户登录"}
              </button>
            </div>

            {/* 隐私政策 */}
            <div
              style={{
                textAlign: "center",
                fontSize: "14px",
                color: "var(--black)",
                opacity: "0.5",
              }}
            >
              By {mode === "login" ? "logging in" : "registering"}, you agree to{" "}
              <a
                href="#"
                style={{
                  color: "var(--primary)",
                  textDecoration: "none",
                  transition: "color 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = "var(--primary-hover)";
                  e.currentTarget.style.textDecoration = "underline";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = "var(--primary)";
                  e.currentTarget.style.textDecoration = "none";
                }}
              >
                Privacy Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
