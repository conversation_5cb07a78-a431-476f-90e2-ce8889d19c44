"use client";

import React, { useEffect, useState } from "react";
import { useUserStore } from "../store/user";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import styles from "./admin-guard.module.scss";

interface AdminGuardProps {
  children: React.ReactNode;
}

export function AdminGuard({ children }: AdminGuardProps) {
  const userStore = useUserStore();
  const navigate = useNavigate();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAdminAccess = async () => {
      // 如果没有登录，先尝试获取用户信息
      if (!userStore.isAuthenticated && userStore.token) {
        await userStore.fetchUserInfo();
      }

      // 检查是否为管理员
      if (!userStore.isAuthenticated || !userStore.isAdmin()) {
        // 非管理员用户，重定向到首页
        navigate(Path.Home);
        return;
      }

      setIsChecking(false);
    };

    checkAdminAccess();
  }, [userStore, navigate]);

  // 显示加载状态
  if (isChecking) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}>
          <div className={styles.spinner}></div>
          <p>验证管理员权限...</p>
        </div>
      </div>
    );
  }

  // 如果不是管理员，显示无权限提示
  if (!userStore.isAuthenticated || !userStore.isAdmin()) {
    return (
      <div className={styles.accessDenied}>
        <div className={styles.accessDeniedContent}>
          <div className={styles.accessDeniedIcon}>🚫</div>
          <h2>访问被拒绝</h2>
          <p>您没有权限访问管理后台</p>
          <button
            onClick={() => navigate(Path.Home)}
            className={styles.backButton}
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  // 管理员用户，显示内容
  return <>{children}</>;
}
