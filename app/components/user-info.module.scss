.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  gap: 16px;
  min-width: 280px;
}

.user-details {
  text-align: center;

  .user-name {
    font-size: 20px;
    font-weight: 600;
    color: var(--black);
    margin-bottom: 6px;
  }

  .user-email {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 12px;
  }

  .user-badges {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    margin-top: 16px;
  }

  .user-role {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid var(--border-in-light);
    background: var(--white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    .role-icon {
      font-size: 16px;
    }

    &.admin-role {
      background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
      color: #8b4513;
      border-color: #ffd700;

      .role-icon {
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
      }
    }

    &.user-role-normal {
      background: var(--second);
      color: var(--primary);
      border-color: var(--border-in-light);
    }
  }

  .user-balance {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 500;
    padding: 10px 16px;
    border-radius: 20px;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
    border: 1px solid #c8e6c9;
    color: #2e7d32;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    .balance-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .balance-icon {
      font-size: 16px;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    .balance-text {
      font-weight: 400;
    }

    .balance-value {
      font-weight: 600;
      font-size: 15px;
    }

    .recharge-button {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: none;
      background: #4caf50;
      color: white;
      font-size: 14px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      line-height: 1;
      margin-left: 4px;
      flex-shrink: 0;

      &:hover {
        background: #45a049;
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
      }

      &:active {
        transform: scale(0.95);
      }
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .user-min-points {
    font-size: 12px;
    color: var(--orange);
    background: rgba(255, 152, 0, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
  }
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}
