.recharge-modal {
  padding: 24px;
  font-family: inherit;
  overflow: hidden;
}

.amount-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 8px;
  text-align: center;
}

.section-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 20px;
  line-height: 1.4;
}

.amount-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.amount-option {
  padding: 16px 12px;
  border: 1px solid var(--border-in-light);
  border-radius: 10px;
  background: var(--white);
  color: var(--black);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  .amount-price {
    font-size: 16px;
    font-weight: 600;
  }

  .amount-points {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 400;
  }

  &:hover {
    border-color: var(--primary);
    background: var(--hover-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  &.selected {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    .amount-points {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.custom-amount {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.custom-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--black);
  cursor: pointer;

  input[type="checkbox"] {
    width: 16px;
    height: 16px;
  }
}

.custom-input {
  display: flex;
  align-items: center;
  border: 2px solid var(--border-in-light);
  border-radius: 8px;
  background: var(--white);
  overflow: hidden;

  .currency {
    padding: 12px 16px;
    background: var(--second);
    color: var(--black);
    font-weight: 500;
    border-right: 1px solid var(--border-in-light);
    flex-shrink: 0;
  }

  input {
    flex: 1;
    padding: 12px 16px;
    border: none;
    outline: none;
    font-size: 16px;
    background: transparent;
    color: var(--black);
    min-width: 0;

    &::placeholder {
      color: var(--text-secondary);
    }
  }

  .points-hint {
    padding: 12px 16px;
    background: var(--second);
    color: var(--primary);
    font-size: 14px;
    font-weight: 500;
    border-left: 1px solid var(--border-in-light);
    flex-shrink: 0;
  }
}

.amount-info {
  padding: 20px;
  background: var(--second);
  border-radius: 12px;
  border: 1px solid var(--border-in-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.balance-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  &.highlight {
    padding-top: 12px;
    border-top: 1px solid var(--border-in-light);
  }
}

.info-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 400;
}

.info-value {
  font-size: 14px;
  color: var(--black);
  font-weight: 500;

  &.points-value {
    color: var(--primary);
    font-weight: 600;
    font-size: 15px;
  }
}

.actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.payment-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.payment-info {
  text-align: center;
}

.payment-summary {
  margin-bottom: 12px;
}

.amount-display {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 4px;
}

.points-display {
  font-size: 16px;
  font-weight: 500;
  color: var(--primary);
}

.countdown {
  font-size: 14px;
  color: var(--orange);
  font-weight: 500;
}

.qrcode-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.qrcode-title {
  font-size: 16px;
  color: var(--black);
  font-weight: 500;
}

.qrcode-container {
  padding: 20px;
  background: var(--white);
  border: 2px solid var(--border-in-light);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.qrcode {
  width: 200px;
  height: 200px;
  display: block;
}

.payment-status {
  text-align: center;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
}

.success-message {
  color: var(--green);
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.error-message {
  color: var(--red);
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.pending-message {
  color: var(--blue);
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.payment-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.error-message {
  color: var(--red);
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  margin: 8px 0;
}

// 响应式设计
@media (max-width: 480px) {
  .recharge-modal {
    width: 90vw;
    max-width: 400px;
    padding: 16px;
  }

  .amount-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .qrcode {
    width: 160px;
    height: 160px;
  }
}
