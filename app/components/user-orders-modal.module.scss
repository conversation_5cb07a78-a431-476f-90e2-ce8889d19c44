.container {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.statsSection {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--second);
  border-radius: 8px;
  border: 1px solid var(--border-in-light);
}

.statItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statLabel {
  font-size: 14px;
  color: var(--text-secondary);
}

.statValue {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary);
}

.filters {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  
  label {
    font-size: 14px;
    font-weight: 500;
    color: var(--black);
  }
  
  select {
    padding: 6px 12px;
    border: 1px solid var(--border-in-light);
    border-radius: 6px;
    font-size: 14px;
    background: var(--white);
    color: var(--black);
    
    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }
}

.ordersSection {
  min-height: 300px;
}

.loading,
.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-secondary);
  font-size: 14px;
}

.ordersList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.orderCard {
  border: 1px solid var(--border-in-light);
  border-radius: 8px;
  background: var(--white);
  overflow: hidden;
  transition: box-shadow 0.2s;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.orderHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--second);
  border-bottom: 1px solid var(--border-in-light);
}

.orderNo {
  font-size: 13px;
  font-family: monospace;
  color: var(--text-secondary);
}

.orderStatus {
  font-size: 13px;
  font-weight: 600;
}

.orderBody {
  padding: 16px;
}

.orderInfo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.infoItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.infoLabel {
  font-size: 12px;
  color: var(--text-secondary);
}

.infoValue {
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
}

.orderTime {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid var(--border-in-light);
}

.timeItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeLabel {
  font-size: 12px;
  color: var(--text-secondary);
}

.timeValue {
  font-size: 12px;
  color: var(--black);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-in-light);
  
  button {
    padding: 6px 12px;
    border: 1px solid var(--border-in-light);
    background: var(--white);
    color: var(--black);
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
    
    &:hover:not(:disabled) {
      background: var(--hover-color);
      border-color: var(--primary);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  span {
    font-size: 13px;
    color: var(--text-secondary);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .statsSection {
    flex-direction: column;
    gap: 12px;
  }
  
  .orderInfo {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .infoItem {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .timeItem {
    font-size: 12px;
  }
  
  .orderHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
