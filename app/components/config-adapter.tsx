import { useEffect, ReactNode, useState } from "react";
import { useSystemConfigStore } from "../store/system-config";
import { useUserStore } from "../store/user";

interface ConfigAdapterProps {
  children: ReactNode;
}

/**
 * 配置适配器组件
 * 负责在应用启动时加载系统配置，确保后端模式正确配置
 * 如果不是后端模式或配置加载失败，将抛出错误
 */
export function ConfigAdapter({ children }: ConfigAdapterProps) {
  const systemConfigStore = useSystemConfigStore();
  const userStore = useUserStore();
  const [configLoaded, setConfigLoaded] = useState(false);
  const [configError, setConfigError] = useState<string | null>(null);
  const [hasRefreshedForAuth, setHasRefreshedForAuth] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    // 应用启动时加载系统配置
    const loadInitialConfig = async () => {
      console.log("[ConfigAdapter] Initializing system configuration...");

      try {
        await systemConfigStore.loadConfig();

        const isBackend = systemConfigStore.isBackendMode();
        const isFrontendDisabled = systemConfigStore.isFrontendConfigDisabled();

        console.log("[ConfigAdapter] Configuration loaded:", {
          backendMode: isBackend,
          frontendConfigDisabled: isFrontendDisabled,
          userAuthenticated: userStore.isAuthenticated,
        });

        // 检查后端模式，但允许一定的容错
        if (!isBackend) {
          console.log("[ConfigAdapter] 后端模式未启用，等待配置加载完成...");

          // 使用更宽容的重试机制
          let retryCount = 0;
          const maxRetries = 3;

          const retryCheck = async () => {
            retryCount++;
            console.log(
              `[ConfigAdapter] 重试检查配置 (${retryCount}/${maxRetries})`,
            );

            try {
              await systemConfigStore.loadConfig();
              const retryIsBackend = systemConfigStore.isBackendMode();

              if (retryIsBackend) {
                console.log(
                  "[ConfigAdapter] ✅ 后端模式配置验证通过（重试后）",
                );
                setConfigLoaded(true);
                setIsInitializing(false);
                return;
              }

              if (retryCount < maxRetries) {
                // 继续重试
                setTimeout(retryCheck, 1000);
              } else {
                // 最后一次重试失败，但允许系统继续运行
                console.warn(
                  "[ConfigAdapter] ⚠️ 后端模式检查失败，但允许系统继续运行",
                );
                setConfigLoaded(true);
                setIsInitializing(false);
              }
            } catch (retryError) {
              console.error("[ConfigAdapter] 重试加载配置失败:", retryError);
              if (retryCount >= maxRetries) {
                // 允许系统继续运行，而不是显示错误
                console.warn(
                  "[ConfigAdapter] ⚠️ 配置加载失败，但允许系统继续运行",
                );
                setConfigLoaded(true);
                setIsInitializing(false);
              } else {
                setTimeout(retryCheck, 1000);
              }
            }
          };

          setTimeout(retryCheck, 1000);
          return;
        }

        // 移除服务商检查 - 让用户自由使用，管理员会自行配置

        console.log("[ConfigAdapter] ✅ 后端模式配置验证通过");
        setConfigLoaded(true);
        setIsInitializing(false);

        // 如果是后端模式且用户已登录，尝试获取用户信息
        if (userStore.token && !userStore.user) {
          console.log(
            "[ConfigAdapter] Backend mode detected, fetching user info...",
          );
          await userStore.fetchUserInfo();
        }
      } catch (error) {
        const errorMsg = `❌ 系统配置加载失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`;
        console.error("[ConfigAdapter]", errorMsg, error);
        setConfigError(errorMsg);
        setIsInitializing(false);
      }
    };

    loadInitialConfig();
  }, []);

  // 监听用户登录状态变化，在后端模式下刷新配置（只刷新一次）
  useEffect(() => {
    if (
      configLoaded &&
      systemConfigStore.isBackendMode() &&
      userStore.isAuthenticated &&
      !hasRefreshedForAuth
    ) {
      console.log(
        "[ConfigAdapter] User authenticated in backend mode, refreshing config...",
      );
      setHasRefreshedForAuth(true);
      systemConfigStore.refreshConfig();
    }
  }, [userStore.isAuthenticated, configLoaded, hasRefreshedForAuth]);

  // 如果配置出错，显示错误页面
  if (configError) {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "100vh",
          padding: "20px",
          textAlign: "center",
          backgroundColor: "#f5f5f5",
        }}
      >
        <div
          style={{
            backgroundColor: "#fff",
            padding: "40px",
            borderRadius: "8px",
            boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
            maxWidth: "600px",
          }}
        >
          <h1 style={{ color: "#e74c3c", marginBottom: "20px" }}>
            系统配置错误
          </h1>
          <p style={{ color: "#666", marginBottom: "30px", lineHeight: "1.6" }}>
            {configError}
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              backgroundColor: "#3498db",
              color: "white",
              border: "none",
              padding: "12px 24px",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "16px",
            }}
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  // 如果正在初始化，显示初始化状态
  if (isInitializing) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100vh",
          backgroundColor: "#f5f5f5",
        }}
      >
        <div
          style={{
            backgroundColor: "#fff",
            padding: "40px",
            borderRadius: "8px",
            boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
            textAlign: "center",
          }}
        >
          <div
            style={{
              width: "40px",
              height: "40px",
              border: "4px solid #f3f3f3",
              borderTop: "4px solid #3498db",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              margin: "0 auto 20px",
            }}
          ></div>
          <p style={{ color: "#666", margin: 0 }}>正在初始化系统...</p>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `}</style>
      </div>
    );
  }

  // 如果配置还在加载中，显示加载页面
  if (!configLoaded) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "100vh",
          backgroundColor: "#f5f5f5",
        }}
      >
        <div
          style={{
            backgroundColor: "#fff",
            padding: "40px",
            borderRadius: "8px",
            boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
            textAlign: "center",
          }}
        >
          <div
            style={{
              width: "40px",
              height: "40px",
              border: "4px solid #f3f3f3",
              borderTop: "4px solid #3498db",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              margin: "0 auto 20px",
            }}
          ></div>
          <p style={{ color: "#666", margin: 0 }}>正在加载系统配置...</p>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `}</style>
      </div>
    );
  }

  // 配置加载完成且验证通过，渲染子组件
  return <>{children}</>;
}

/**
 * 配置状态显示组件（用于调试）
 */
export function ConfigDebugInfo() {
  const systemConfigStore = useSystemConfigStore();
  const userStore = useUserStore();

  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  const { config, isLoading, error } = systemConfigStore;

  return (
    <div
      style={{
        position: "fixed",
        top: "10px",
        right: "10px",
        background: "rgba(0, 0, 0, 0.8)",
        color: "white",
        padding: "10px",
        borderRadius: "5px",
        fontSize: "12px",
        zIndex: 9999,
        maxWidth: "300px",
      }}
    >
      <div>
        <strong>配置调试信息</strong>
      </div>
      <div>加载中: {isLoading ? "是" : "否"}</div>
      <div>错误: {error || "无"}</div>
      <div>后端模式: {config?.backendMode ? "是" : "否"}</div>
      <div>前端配置禁用: {config?.frontendConfigDisabled ? "是" : "否"}</div>
      <div>隐藏API Key: {config?.hideUserApiKey ? "是" : "否"}</div>
      <div>可用服务商: {config?.providers?.length || 0}</div>
      <div>用户已登录: {userStore.isAuthenticated ? "是" : "否"}</div>
      <div>用户角色: {userStore.user?.role || "未知"}</div>
    </div>
  );
}
