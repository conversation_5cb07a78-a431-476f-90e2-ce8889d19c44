.auth-page {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  width: 100%;
  flex-direction: column;
  .top-banner {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 64px;
    box-sizing: border-box;
    background: var(--second);
    .top-banner-inner {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      line-height: 150%;
      span {
        gap: 8px;
        a {
          display: inline-flex;
          align-items: center;
          text-decoration: none;
          margin-left: 8px;
          color: var(--primary);
        }
      }
    }
    .top-banner-close {
      cursor: pointer;
      position: absolute;
      top: 50%;
      right: 48px;
      transform: translateY(-50%);
    }
  }

  @media (max-width: 600px) {
    .top-banner {
      padding: 12px 24px 12px 12px;
      .top-banner-close {
        right: 10px;
      }
      .top-banner-inner {
        .top-banner-logo {
          margin-right: 8px;
        }
      }
    }
  }

  .auth-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    animation: slide-in-from-top ease 0.3s;
  }

  .auth-logo {
    margin-top: 10vh;
    transform: scale(1.4);
  }

  .auth-title {
    font-size: 24px;
    font-weight: bold;
    line-height: 2;
    margin-bottom: 1vh;
  }

  .auth-tips {
    font-size: 14px;
  }

  .auth-input {
    margin: 3vh 0;
  }

  .auth-input-second {
    margin: 0 0 3vh 0;
  }

  .auth-actions {
    display: flex;
    justify-content: center;
    flex-direction: column;

    button:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}

/* 现代化登录页面样式 */
.modern-auth-page {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--auth-bg, #0a0a0a);
  overflow: hidden;
  transition: background-color 0.3s ease;
}

.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.auth-background-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

.auth-background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 30% 20%,
    rgba(59, 130, 246, 0.15) 0%,
    transparent 50%
  ),
  radial-gradient(
    circle at 70% 80%,
    rgba(139, 92, 246, 0.15) 0%,
    transparent 50%
  );
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

.auth-header {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.return-button {
  background: var(--auth-input-bg, rgba(255, 255, 255, 0.1)) !important;
  border: 1px solid var(--auth-input-border, rgba(255, 255, 255, 0.2)) !important;
  color: var(--auth-text-secondary, rgba(255, 255, 255, 0.8)) !important;
  backdrop-filter: blur(10px);
  border-radius: 12px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: var(--auth-input-bg, rgba(255, 255, 255, 0.15)) !important;
    color: var(--auth-text-primary, white) !important;
    transform: translateY(-1px);
  }
}

.theme-button {
  background: var(--auth-input-bg, rgba(255, 255, 255, 0.1)) !important;
  border: 1px solid var(--auth-input-border, rgba(255, 255, 255, 0.2)) !important;
  color: var(--auth-text-secondary, rgba(255, 255, 255, 0.8)) !important;
  backdrop-filter: blur(10px);
  border-radius: 12px !important;
  padding: 8px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  &:hover {
    background: var(--auth-input-bg, rgba(255, 255, 255, 0.15)) !important;
    color: var(--auth-text-primary, white) !important;
    transform: translateY(-1px);
  }

  svg {
    width: 18px !important;
    height: 18px !important;
  }
}

.auth-container {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  background: var(--auth-card-bg, rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid var(--auth-border, rgba(255, 255, 255, 0.1));
  box-shadow: var(--auth-shadow, 0 20px 40px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    max-width: 90%;
    margin: 0 5%;
  }

  @media (max-width: 480px) {
    max-width: 95%;
    margin: 0 2.5%;
    border-radius: 16px;
  }
}



.auth-form-container {
  width: 100%;
}

.auth-form {
  padding: 40px 48px;
  width: 100%;

  @media (max-width: 768px) {
    padding: 32px 24px;
  }

  @media (max-width: 480px) {
    padding: 24px 20px;
  }
}

.auth-form-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--auth-text-primary, white);
  margin: 0 0 8px 0;
  text-align: center;
  transition: color 0.3s ease;
}

.auth-form-subtitle {
  font-size: 14px;
  color: var(--auth-text-secondary, rgba(255, 255, 255, 0.7));
  text-align: center;
  margin: 0 0 32px 0;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.auth-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  text-align: center;
  margin-bottom: 24px;
}

.auth-form-fields {
  margin-bottom: 24px;
}

.auth-field {
  margin-bottom: 16px;
}

.auth-input {
  width: 100%;
  padding: 16px 20px;
  background: var(--auth-input-bg, rgba(255, 255, 255, 0.05));
  border: 1px solid var(--auth-input-border, rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  color: var(--auth-input-text, white);
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;

  &::placeholder {
    color: var(--auth-text-secondary, rgba(255, 255, 255, 0.5));
  }

  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: var(--auth-input-bg, rgba(255, 255, 255, 0.08));
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

.auth-actions {
  margin-bottom: 24px;
}

.auth-primary-button {
  width: 100%;
  padding: 16px;
  background: var(--auth-button-bg, linear-gradient(135deg, #3b82f6, #8b5cf6));
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;

  &:hover:not(:disabled) {
    background: var(--auth-button-hover, linear-gradient(135deg, #2563eb, #7c3aed));
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.auth-divider {
  text-align: center;
  margin: 16px 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
  }

  span {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.6);
    padding: 0 16px;
    font-size: 14px;
    position: relative;
    z-index: 1;
  }
}

.auth-secondary-button {
  width: 100%;
  padding: 16px;
  background: var(--auth-input-bg, rgba(255, 255, 255, 0.05));
  border: 1px solid var(--auth-input-border, rgba(255, 255, 255, 0.1));
  border-radius: 12px;
  color: var(--auth-text-secondary, rgba(255, 255, 255, 0.8));
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: var(--auth-input-bg, rgba(255, 255, 255, 0.1));
    color: var(--auth-text-primary, white);
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.auth-privacy {
  text-align: center;
  font-size: 12px;
  color: var(--auth-text-secondary, rgba(255, 255, 255, 0.5));
  line-height: 1.5;
  transition: color 0.3s ease;
}

.auth-privacy-link {
  color: #3b82f6;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    max-width: 95%;
    margin: 20px auto;
  }

  .auth-form {
    padding: 40px 30px;
  }

  .auth-form-title {
    font-size: 24px;
  }
}
