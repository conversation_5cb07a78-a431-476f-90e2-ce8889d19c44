.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--gray);
}

.loadingSpinner {
  text-align: center;
  padding: 40px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);

  p {
    margin-top: 16px;
    color: var(--text-secondary);
    font-size: 14px;
  }
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.accessDenied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--gray);
  padding: 20px;
}

.accessDeniedContent {
  text-align: center;
  padding: 60px 40px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  max-width: 400px;
  width: 100%;
}

.accessDeniedIcon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.8;
}

.accessDeniedContent h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.accessDeniedContent p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.backButton {
  background: linear-gradient(135deg, var(--primary), var(--primary-hover));
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(29, 147, 171, 0.3);
  }

  &::before {
    content: '🏠';
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .accessDeniedContent {
    padding: 40px 24px;
    margin: 16px;
  }

  .accessDeniedIcon {
    font-size: 48px;
    margin-bottom: 20px;
  }

  .accessDeniedContent h2 {
    font-size: 20px;
  }

  .accessDeniedContent p {
    font-size: 14px;
  }
}
