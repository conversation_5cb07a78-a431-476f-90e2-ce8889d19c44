"use client";

import React, { useState, useEffect } from "react";
import { CustomModal } from "./custom-modal";
import { useUserStore } from "../store/user";
import styles from "./user-orders-modal.module.scss";

interface UserOrder {
  id: string;
  outTradeNo: string;
  amount: number;
  status: string;
  payChannel: string;
  tradeType: string;
  createdAt: string;
  successTime?: string;
  expireTime?: string;
}

interface UserOrdersModalProps {
  show: boolean;
  onClose: () => void;
}

const STATUS_MAP = {
  PENDING: "待支付",
  PAID: "已支付",
  FAILED: "支付失败",
  EXPIRED: "已过期",
  CANCELLED: "已取消",
};

const STATUS_COLORS = {
  PENDING: "#f39c12",
  PAID: "#27ae60",
  FAILED: "#e74c3c",
  EXPIRED: "#95a5a6",
  CANCELLED: "#95a5a6",
};

export function UserOrdersModal({ show, onClose }: UserOrdersModalProps) {
  const userStore = useUserStore();
  const [orders, setOrders] = useState<UserOrder[]>([]);
  const [stats, setStats] = useState<
    Record<string, { count: number; amount: number }>
  >({});
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState("ALL");

  const fetchOrders = async () => {
    if (!show || !userStore.token) return;

    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
      });

      if (statusFilter !== "ALL") {
        params.append("status", statusFilter);
      }

      const response = await fetch(`/api/user/orders?${params}`, {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      });

      const result = await response.json();

      if (result.success) {
        setOrders(result.data.orders);
        setStats(result.data.stats);
        setTotalPages(result.data.pagination.totalPages);
      } else {
        console.error("Failed to fetch orders:", result.error);
      }
    } catch (error) {
      console.error("Fetch orders error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (show) {
      setCurrentPage(1);
      fetchOrders();
    }
  }, [show, statusFilter]);

  useEffect(() => {
    fetchOrders();
  }, [currentPage]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN");
  };

  const formatAmount = (amount: number) => {
    return `¥${amount.toFixed(2)}`;
  };

  const getStatsValue = (status: string, field: "count" | "amount") => {
    return stats[status]?.[field] || 0;
  };

  if (!show) return null;

  return (
    <CustomModal
      isOpen={show}
      onClose={onClose}
      title="历史订单"
      width="800px"
      maxWidth="95vw"
    >
      <div className={styles.container}>
        {/* 统计信息 */}
        <div className={styles.statsSection}>
          <div className={styles.statItem}>
            <span className={styles.statLabel}>成功充值：</span>
            <span className={styles.statValue}>
              {getStatsValue("PAID", "count")} 笔 /{" "}
              {formatAmount(getStatsValue("PAID", "amount"))}
            </span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statLabel}>失败充值：</span>
            <span className={styles.statValue}>
              {getStatsValue("FAILED", "count")} 笔 /{" "}
              {formatAmount(getStatsValue("FAILED", "amount"))}
            </span>
          </div>
        </div>

        {/* 筛选器 */}
        <div className={styles.filters}>
          <label>状态筛选：</label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="ALL">全部</option>
            {Object.entries(STATUS_MAP).map(([key, label]) => (
              <option key={key} value={key}>
                {label}
              </option>
            ))}
          </select>
        </div>

        {/* 订单列表 */}
        <div className={styles.ordersSection}>
          {loading ? (
            <div className={styles.loading}>加载中...</div>
          ) : orders.length === 0 ? (
            <div className={styles.empty}>暂无订单记录</div>
          ) : (
            <div className={styles.ordersList}>
              {orders.map((order) => (
                <div key={order.id} className={styles.orderCard}>
                  <div className={styles.orderHeader}>
                    <div className={styles.orderNo}>
                      订单号：{order.outTradeNo}
                    </div>
                    <div
                      className={styles.orderStatus}
                      style={{
                        color:
                          STATUS_COLORS[
                            order.status as keyof typeof STATUS_COLORS
                          ],
                      }}
                    >
                      {STATUS_MAP[order.status as keyof typeof STATUS_MAP]}
                    </div>
                  </div>

                  <div className={styles.orderBody}>
                    <div className={styles.orderInfo}>
                      <div className={styles.infoItem}>
                        <span className={styles.infoLabel}>充值金额：</span>
                        <span className={styles.infoValue}>
                          {formatAmount(order.amount)}
                        </span>
                      </div>
                      <div className={styles.infoItem}>
                        <span className={styles.infoLabel}>获得积分：</span>
                        <span className={styles.infoValue}>
                          {(order.amount * 10).toFixed(0)} ☁️Point
                        </span>
                      </div>
                      <div className={styles.infoItem}>
                        <span className={styles.infoLabel}>支付方式：</span>
                        <span className={styles.infoValue}>
                          {order.payChannel === "wxpay"
                            ? "微信支付"
                            : order.payChannel}
                        </span>
                      </div>
                    </div>

                    <div className={styles.orderTime}>
                      <div className={styles.timeItem}>
                        <span className={styles.timeLabel}>创建时间：</span>
                        <span className={styles.timeValue}>
                          {formatDate(order.createdAt)}
                        </span>
                      </div>
                      {order.successTime && (
                        <div className={styles.timeItem}>
                          <span className={styles.timeLabel}>完成时间：</span>
                          <span className={styles.timeValue}>
                            {formatDate(order.successTime)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className={styles.pagination}>
            <button
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              上一页
            </button>
            <span>
              第 {currentPage} 页，共 {totalPages} 页
            </span>
            <button
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              下一页
            </button>
          </div>
        )}
      </div>
    </CustomModal>
  );
}
