import { useState, useEffect } from "react";
import { CustomModal } from "./custom-modal";
import { IconButton } from "./button";
import { useUserStore } from "../store/user";
import { usePaymentStore, PaymentUtils } from "../store/payment";
import styles from "./recharge-modal.module.scss";
import Locale from "../locales";

interface RechargeModalProps {
  show: boolean;
  onClose: () => void;
}

interface PaymentOrder {
  orderId: string;
  outTradeNo: string;
  amount: number;
  codeUrl: string;
  qrcodeUrl: string;
  expireTime: string;
}

interface PaymentStatus {
  orderId: string;
  outTradeNo: string;
  amount: number;
  status: "PENDING" | "PAID" | "FAILED" | "EXPIRED" | "CANCELLED";
  successTime?: string;
  expireTime?: string;
}

const AMOUNT_OPTIONS = [10, 20, 50, 100, 200, 500];

export function RechargeModal({ show, onClose }: RechargeModalProps) {
  const userStore = useUserStore();
  const paymentStore = usePaymentStore();
  const [selectedAmount, setSelectedAmount] = useState<number>(20);
  const [customAmount, setCustomAmount] = useState<string>("");
  const [isCustom, setIsCustom] = useState(false);
  const [loading, setLoading] = useState(false);
  const [paymentOrder, setPaymentOrder] = useState<PaymentOrder | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(
    null,
  );
  const [countdown, setCountdown] = useState<number>(0);
  const [error, setError] = useState<string>("");

  // 获取实际充值金额
  const getAmount = () => {
    if (isCustom) {
      const amount = parseFloat(customAmount);
      return isNaN(amount) ? 0 : amount;
    }
    return selectedAmount;
  };

  // 创建支付订单
  const createPaymentOrder = async () => {
    const amount = getAmount();
    if (amount <= 0) {
      setError("请输入有效的充值金额");
      return;
    }

    if (amount < 1) {
      setError("充值金额不能少于1元");
      return;
    }

    if (amount > 10000) {
      setError("单次充值金额不能超过10000元");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/payment/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userStore.token}`,
        },
        body: JSON.stringify({ amount }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log("Payment create response:", result);

      if (result.success) {
        const order = result.data;
        setPaymentOrder(order);

        // 添加到支付状态管理
        paymentStore.addOrder({
          id: order.orderId,
          outTradeNo: order.outTradeNo,
          amount: order.amount,
          status: "PENDING",
          codeUrl: order.codeUrl,
          qrcodeUrl: order.qrcodeUrl,
          expireTime: order.expireTime,
          createdAt: new Date().toISOString(),
        });

        // 开始倒计时
        const expireTime = new Date(order.expireTime).getTime();
        const now = Date.now();
        setCountdown(Math.max(0, Math.floor((expireTime - now) / 1000)));
        // 开始轮询支付状态
        startStatusPolling(order.orderId);
      } else {
        const errorMsg = result.message || "创建支付订单失败";
        console.error("Payment create failed:", errorMsg);
        setError(errorMsg);
      }
    } catch (error) {
      console.error("Create payment order error:", error);
      if (error instanceof Error) {
        if (error.message.includes("HTTP 401")) {
          setError("登录已过期，请重新登录");
        } else if (error.message.includes("HTTP 500")) {
          setError("服务器内部错误，请稍后重试");
        } else if (error.message.includes("签名")) {
          setError("支付服务暂时不可用，请稍后重试或联系客服");
        } else {
          setError(error.message);
        }
      } else {
        setError("网络连接失败，请检查网络后重试");
      }
    } finally {
      setLoading(false);
    }
  };

  // 轮询支付状态
  const startStatusPolling = (orderId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/payment/status?orderId=${orderId}`, {
          headers: {
            Authorization: `Bearer ${userStore.token}`,
          },
        });

        const result = await response.json();

        if (result.success) {
          setPaymentStatus(result.data);

          // 更新支付状态管理中的订单状态
          paymentStore.updateOrder(orderId, {
            status: result.data.status,
            successTime: result.data.successTime,
          });

          if (result.data.status === "PAID") {
            clearInterval(pollInterval);
            // 刷新用户信息
            await userStore.fetchUserInfo();
            // 3秒后自动关闭
            setTimeout(() => {
              onClose();
            }, 3000);
          } else if (
            result.data.status === "FAILED" ||
            result.data.status === "EXPIRED"
          ) {
            clearInterval(pollInterval);
          }
        }
      } catch (error) {
        console.error("Poll payment status error:", error);
      }
    }, 2000); // 每2秒查询一次

    // 30分钟后停止轮询
    setTimeout(
      () => {
        clearInterval(pollInterval);
      },
      30 * 60 * 1000,
    );
  };

  // 倒计时效果
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 重置状态
  const resetState = () => {
    setPaymentOrder(null);
    setPaymentStatus(null);
    setCountdown(0);
    setError("");
    setCustomAmount("");
    setIsCustom(false);
    setSelectedAmount(20);
    paymentStore.clearCurrentOrder();
  };

  // 关闭处理
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 格式化倒计时
  const formatCountdown = (seconds: number) => {
    return PaymentUtils.formatCountdown(seconds);
  };

  if (!show) return null;

  return (
    <CustomModal
      isOpen={show}
      onClose={handleClose}
      title="账户充值"
      width="600px"
      maxWidth="90vw"
    >
      <div className={styles["recharge-modal"]}>
        {!paymentOrder ? (
          // 金额选择界面
          <div className={styles["amount-selection"]}>
            <div className={styles["section-title"]}>选择充值金额</div>
            <div className={styles["section-subtitle"]}>
              充值后将获得10倍的 ☁️Point，可用于AI对话消费
            </div>

            <div className={styles["amount-options"]}>
              {AMOUNT_OPTIONS.map((amount) => (
                <button
                  key={amount}
                  className={`${styles["amount-option"]} ${
                    !isCustom && selectedAmount === amount
                      ? styles["selected"]
                      : ""
                  }`}
                  onClick={() => {
                    setSelectedAmount(amount);
                    setIsCustom(false);
                    setError("");
                  }}
                >
                  <div className={styles["amount-price"]}>¥{amount}</div>
                  <div className={styles["amount-points"]}>
                    ={amount * 10} ☁️Point
                  </div>
                </button>
              ))}
            </div>

            <div className={styles["custom-amount"]}>
              <label className={styles["custom-label"]}>
                <input
                  type="checkbox"
                  checked={isCustom}
                  onChange={(e) => {
                    setIsCustom(e.target.checked);
                    setError("");
                  }}
                />
                自定义金额
              </label>
              {isCustom && (
                <div className={styles["custom-input"]}>
                  <span className={styles["currency"]}>¥</span>
                  <input
                    type="number"
                    value={customAmount}
                    onChange={(e) => setCustomAmount(e.target.value)}
                    placeholder="请输入金额"
                    min="1"
                    max="10000"
                    step="0.01"
                  />
                  <span className={styles["points-hint"]}>
                    = {(getAmount() * 10).toFixed(0)} ☁️Point
                  </span>
                </div>
              )}
            </div>

            {error && <div className={styles["error-message"]}>{error}</div>}

            <div className={styles["amount-info"]}>
              <div className={styles["balance-row"]}>
                <span className={styles["info-label"]}>当前余额</span>
                <span className={styles["info-value"]}>
                  {(userStore.user?.pointsBalance || 0).toFixed(2)} ☁️Point
                </span>
              </div>
              <div className={styles["balance-row"]}>
                <span className={styles["info-label"]}>充值金额</span>
                <span className={styles["info-value"]}>
                  ¥{getAmount().toFixed(2)}
                </span>
              </div>
              <div
                className={`${styles["balance-row"]} ${styles["highlight"]}`}
              >
                <span className={styles["info-label"]}>获得点数</span>
                <span
                  className={`${styles["info-value"]} ${styles["points-value"]}`}
                >
                  +{(getAmount() * 10).toFixed(0)} ☁️Point
                </span>
              </div>
            </div>

            <div className={styles["actions"]}>
              <IconButton
                text="取消"
                onClick={handleClose}
                shadow
                style={{
                  background: "var(--second)",
                  color: "var(--primary)",
                  border: "1px solid var(--border-in-light)",
                }}
              />
              <IconButton
                text={loading ? "创建中..." : "立即充值"}
                onClick={createPaymentOrder}
                disabled={loading || getAmount() <= 0}
                shadow
                style={{
                  background: "var(--primary)",
                  color: "white",
                }}
              />
            </div>
          </div>
        ) : (
          // 支付界面
          <div className={styles["payment-section"]}>
            <div className={styles["payment-info"]}>
              <div className={styles["payment-summary"]}>
                <div className={styles["amount-display"]}>
                  充值 ¥{paymentOrder.amount.toFixed(2)}
                </div>
                <div className={styles["points-display"]}>
                  获得 {(paymentOrder.amount * 10).toFixed(0)} ☁️Point
                </div>
              </div>
              {countdown > 0 && (
                <div className={styles["countdown"]}>
                  剩余时间: {formatCountdown(countdown)}
                </div>
              )}
            </div>

            <div className={styles["qrcode-section"]}>
              <div className={styles["qrcode-title"]}>请使用微信扫码支付</div>
              <div className={styles["qrcode-container"]}>
                <img
                  src={paymentOrder.qrcodeUrl}
                  alt="支付二维码"
                  className={styles["qrcode"]}
                />
              </div>
            </div>

            <div className={styles["payment-status"]}>
              {paymentStatus?.status === "PAID" ? (
                <div className={styles["success-message"]}>
                  ✅ 支付成功！已获得 {(paymentOrder.amount * 10).toFixed(0)}{" "}
                  ☁️Point，即将自动关闭...
                </div>
              ) : paymentStatus?.status === "FAILED" ? (
                <div className={styles["error-message"]}>
                  ❌ 支付失败，请重新尝试
                </div>
              ) : paymentStatus?.status === "EXPIRED" ? (
                <div className={styles["error-message"]}>
                  ⏰ 订单已过期，请重新创建订单
                </div>
              ) : (
                <div className={styles["pending-message"]}>等待支付中...</div>
              )}
            </div>

            <div className={styles["payment-actions"]}>
              <IconButton
                text="返回"
                onClick={() => {
                  setPaymentOrder(null);
                  setPaymentStatus(null);
                  setCountdown(0);
                }}
                shadow
                style={{
                  background: "var(--second)",
                  color: "var(--primary)",
                  border: "1px solid var(--border-in-light)",
                }}
              />
              <IconButton
                text="关闭"
                onClick={handleClose}
                shadow
                style={{
                  background: "var(--primary)",
                  color: "white",
                }}
              />
            </div>
          </div>
        )}
      </div>
    </CustomModal>
  );
}
