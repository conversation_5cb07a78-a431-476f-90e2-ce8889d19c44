import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUserStore } from "../store/user";
import { Path } from "../constant";
import { IconButton } from "./button";
import { Modal, showConfirm } from "./ui-lib";
import { UserUsageStats } from "./user-usage-stats";
import { UserOrdersModal } from "./user-orders-modal";
import { UserHistoryLogs } from "./user-history-logs";
import { RechargeModal } from "./recharge-modal";
import Locale from "../locales";
import styles from "./user-info.module.scss";

interface UserInfoProps {
  onClose?: () => void;
}

export function UserInfo({ onClose }: UserInfoProps) {
  const userStore = useUserStore();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [showUsageStats, setShowUsageStats] = useState(false);
  const [showOrders, setShowOrders] = useState(false);
  const [showHistoryLogs, setShowHistoryLogs] = useState(false);
  const [showRechargeModal, setShowRechargeModal] = useState(false);

  // 如果未登录，跳转到登录页面
  useEffect(() => {
    if (!userStore.isAuthenticated) {
      navigate(Path.Auth);
    }
  }, [userStore.isAuthenticated, navigate]);

  const handleLogout = () => {
    showConfirm({
      title: "确认登出",
      content: "您确定要登出当前账户吗？",
      onConfirm: async () => {
        await userStore.logout();
        navigate(Path.Home);
        onClose?.();
      },
    });
  };

  const handleAdminPanel = () => {
    navigate(Path.Settings);
    onClose?.();
  };

  const handleUsageStats = () => {
    setShowUsageStats(true);
  };

  const handleOrders = () => {
    setShowOrders(true);
  };

  const handleHistoryLogs = () => {
    setShowHistoryLogs(true);
  };

  const handleRecharge = () => {
    setShowRechargeModal(true);
  };

  if (!userStore.user) {
    return null;
  }

  return (
    <div className={styles["user-info"]}>
      <div className={styles["user-details"]}>
        <div className={styles["user-name"]}>{userStore.user.username}</div>
        <div className={styles["user-email"]}>{userStore.user.email}</div>

        <div className={styles["user-badges"]}>
          <div
            className={`${styles["user-role"]} ${
              userStore.user.role === "ADMIN"
                ? styles["admin-role"]
                : styles["user-role-normal"]
            }`}
          >
            {userStore.user.role === "ADMIN" ? (
              <>
                <span className={styles["role-icon"]}>👑</span>
                <span>管理员</span>
              </>
            ) : (
              <>
                <span className={styles["role-icon"]}>👤</span>
                <span>普通用户</span>
              </>
            )}
          </div>

          <div className={styles["user-balance"]}>
            <div className={styles["balance-info"]}>
              <span className={styles["balance-icon"]}>☁️</span>
              <span className={styles["balance-text"]}>点数余额</span>
              <span className={styles["balance-value"]}>
                {(userStore.user.pointsBalance || 0).toFixed(2)}
              </span>
            </div>
            <button
              className={styles["recharge-button"]}
              onClick={handleRecharge}
              title="充值"
            >
              +
            </button>
          </div>
        </div>
      </div>

      <div className={styles["user-actions"]}>
        <IconButton
          text="使用统计"
          onClick={handleUsageStats}
          shadow
          style={{
            background: "var(--second)",
            color: "var(--primary)",
            border: "1px solid var(--border-in-light)",
          }}
        />

        <IconButton
          text="使用历史"
          onClick={handleHistoryLogs}
          shadow
          style={{
            background: "var(--second)",
            color: "var(--primary)",
            border: "1px solid var(--border-in-light)",
          }}
        />

        <IconButton
          text="历史订单"
          onClick={handleOrders}
          shadow
          style={{
            background: "var(--second)",
            color: "var(--primary)",
            border: "1px solid var(--border-in-light)",
          }}
        />

        {userStore.isAdmin() && (
          <IconButton
            text="管理后台"
            onClick={handleAdminPanel}
            shadow
            style={{
              background: "var(--primary)",
              color: "white",
            }}
          />
        )}

        <IconButton
          text="登出"
          onClick={handleLogout}
          shadow
          style={{
            background: "var(--error-color)",
            color: "white",
          }}
        />
      </div>

      {/* 使用统计模态框 */}
      <UserUsageStats
        visible={showUsageStats}
        onClose={() => setShowUsageStats(false)}
      />

      {/* 使用历史模态框 */}
      <UserHistoryLogs
        visible={showHistoryLogs}
        onClose={() => setShowHistoryLogs(false)}
      />

      {/* 充值模态框 */}
      <RechargeModal
        show={showRechargeModal}
        onClose={() => setShowRechargeModal(false)}
      />

      {/* 历史订单模态框 */}
      <UserOrdersModal show={showOrders} onClose={() => setShowOrders(false)} />
    </div>
  );
}

export function UserInfoModal({
  show,
  onClose,
}: {
  show: boolean;
  onClose: () => void;
}) {
  if (!show) return null;

  return (
    <div
      className="modal-mask"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <Modal title="用户信息" onClose={onClose}>
        <UserInfo onClose={onClose} />
      </Modal>
    </div>
  );
}
